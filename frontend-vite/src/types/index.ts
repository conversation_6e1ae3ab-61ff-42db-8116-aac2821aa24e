// User and Authentication Types
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile: UserProfile;
}

export interface UserProfile {
  role: 'sales' | 'marketing' | 'production';
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user_id: number;
  username: string;
  role: string;
}

// Lead Types
export interface CatalogueItem {
  id: number;
  image: string;
  name?: string;
  price?: number;
}

export interface Lead {
  id: number;
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  status: string;
  remarks: string;
  created_at: string;
  updated_at: string;
  design_reference: string | null;
  jewelry_categories: string;
  store: string;
  catalogue_selection?: CatalogueItem[] | null;
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

export interface LeadFormData {
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  remarks: string;
  jewelry_categories: string;
  store: string;
  design_file?: File | null;
  catalogue_ids?: number[];
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

// Filter Types
export interface LeadFilters {
  status: string;
  occasion: string;
  store: string;
  jewelry_type: string;
  minBudget: string;
  maxBudget: string;
}

// Order Types
export interface OrderReview {
  id: number;
  lead: Lead;
  approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface ConfirmedOrder {
  id: number;
  lead: Lead;
  cad_file?: string;
  created_at: string;
  updated_at: string;
}

// Chart and Analytics Types
export interface FunnelDataItem {
  status: string;
  count: number;
  color: string;
  percentage: number;
}

export interface InsightsData {
  occasionBreakdown: Record<string, number>;
  totalBudget: number;
  leadCount: number;
  premiumLeads: number;
  conversionRate: number;
  monthlyGrowth: number;
}

// Component Props Types
export interface SegmentationTag {
  label: string;
  color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

// Animation Types
export interface AnimationProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

// Store Types
export interface AuthStore {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

export interface LeadFormStore {
  formData: LeadFormData;
  isSubmitting: boolean;
  updateField: (field: keyof LeadFormData, value: any) => void;
  resetForm: () => void;
  submitForm: () => Promise<void>;
}

// API Response Types
export interface ApiResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ApiError {
  message: string;
  status: number;
  data?: any;
}
