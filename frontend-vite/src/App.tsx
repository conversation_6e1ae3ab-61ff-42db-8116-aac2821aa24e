import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { AnimatePresence } from 'framer-motion';
import { useAuthStore } from './stores/authStore';
import LoginPage from './pages/LoginPage';
import SalesPage from './pages/SalesPage';
import MarketingPage from './pages/MarketingPage';
import ProductionPage from './pages/ProductionPage';
import LeadDetailPage from './pages/LeadDetailPage';
import Layout from './components/layout/Layout';
import LoadingScreen from './components/common/LoadingScreen';

// Role-based route protection component
const ProtectedRoute: React.FC<{
  children: React.ReactNode;
  allowedRoles: string[];
  userRole?: string;
}> = ({ children, allowedRoles, userRole }) => {
  if (!userRole || !allowedRoles.includes(userRole)) {
    // Redirect to appropriate dashboard based on role
    const redirectPath = userRole === 'sales' ? '/sales'
                       : userRole === 'marketing' ? '/marketing'
                       : userRole === 'production' ? '/production'
                       : '/login';
    return <Navigate to={redirectPath} replace />;
  }
  return <>{children}</>;
};

function App() {
  const { isAuthenticated, checkAuth, user } = useAuthStore();
  const [isLoading, setIsLoading] = React.useState(true);

  useEffect(() => {
    const initAuth = async () => {
      await checkAuth();
      setIsLoading(false);
    };
    initAuth();
  }, [checkAuth]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  // Get default route based on user role
  const getDefaultRoute = () => {
    const userRole = user?.profile?.role;
    switch (userRole) {
      case 'sales': return '/sales';
      case 'marketing': return '/marketing';
      case 'production': return '/production';
      default: return '/sales';
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Layout>
        <AnimatePresence mode="wait">
          <Routes>
            <Route path="/" element={<Navigate to={getDefaultRoute()} replace />} />
            <Route
              path="/sales"
              element={
                <ProtectedRoute allowedRoles={['sales']} userRole={user?.profile?.role}>
                  <SalesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/marketing"
              element={
                <ProtectedRoute allowedRoles={['marketing']} userRole={user?.profile?.role}>
                  <MarketingPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/lead/:id"
              element={
                <ProtectedRoute allowedRoles={['marketing', 'sales']} userRole={user?.profile?.role}>
                  <LeadDetailPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/production"
              element={
                <ProtectedRoute allowedRoles={['production']} userRole={user?.profile?.role}>
                  <ProductionPage />
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<Navigate to={getDefaultRoute()} replace />} />
          </Routes>
        </AnimatePresence>
      </Layout>
    </Box>
  );
}

export default App;
