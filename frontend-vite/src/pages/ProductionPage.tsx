import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Add,
  CloudUpload,
  Visibility,
  CheckCircle,
  Schedule,
  Build,
  Assignment,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import PageTransition from '../components/animations/PageTransition';
import StaggeredFadeIn from '../components/animations/StaggeredFadeIn';
import AnimatedButton from '../components/common/AnimatedButton';
import AnimatedCard from '../components/common/AnimatedCard';
import {
  getLeads,
  getOrderReviews,
  getConfirmedOrders,
  createOrderReview,
  approveOrderReview,
  createConfirmedOrder,
  uploadCadFile,
} from '../services/api';
import type { Lead, OrderReview, ConfirmedOrder } from '../types';

const ProductionPage: React.FC = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [orderReviews, setOrderReviews] = useState<OrderReview[]>([]);
  const [confirmedOrders, setConfirmedOrders] = useState<ConfirmedOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [createOrderDialogOpen, setCreateOrderDialogOpen] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [cadFile, setCadFile] = useState<File | null>(null);
  const [uploadingCad, setUploadingCad] = useState<number | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [leadsResponse, reviewsResponse, ordersResponse] = await Promise.all([
        getLeads({ status: 'converted' }),
        getOrderReviews(),
        getConfirmedOrders(),
      ]);

      setLeads(leadsResponse.results || leadsResponse);
      setOrderReviews(reviewsResponse.results || reviewsResponse);
      setConfirmedOrders(ordersResponse.results || ordersResponse);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrderReview = async () => {
    if (!selectedLead) return;

    try {
      await createOrderReview({ lead: selectedLead.id });
      setCreateOrderDialogOpen(false);
      setSelectedLead(null);
      fetchData();
    } catch (error) {
      console.error('Error creating order review:', error);
    }
  };

  const handleApproveOrder = async (reviewId: number) => {
    try {
      await approveOrderReview(reviewId);

      // Create confirmed order
      const review = orderReviews.find(r => r.id === reviewId);
      if (review) {
        await createConfirmedOrder({ lead: review.lead.id });
      }

      fetchData();
    } catch (error) {
      console.error('Error approving order:', error);
    }
  };

  const handleCadUpload = async (orderId: number, file: File) => {
    setUploadingCad(orderId);
    try {
      await uploadCadFile(orderId, file);
      fetchData();
    } catch (error) {
      console.error('Error uploading CAD file:', error);
    } finally {
      setUploadingCad(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'in_production': return 'info';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  return (
    <PageTransition>
      <Container maxWidth="xl">
        <StaggeredFadeIn>
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #FF9800 30%, #FF5722 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Production Dashboard
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Manage orders, track production, and handle CAD files
            </Typography>
          </Box>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <AnimatedCard delay={0.1}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Assignment sx={{ color: 'warning.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Pending Reviews
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    {orderReviews.filter(r => !r.approved).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Orders awaiting approval
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <AnimatedCard delay={0.2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Build sx={{ color: 'info.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      In Production
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'info.main' }}>
                    {confirmedOrders.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active production orders
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <AnimatedCard delay={0.3}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CheckCircle sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Completed
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'success.main' }}>
                    {confirmedOrders.filter(o => o.cad_file).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Orders with CAD files
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <AnimatedCard delay={0.4}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Schedule sx={{ color: 'secondary.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Converted Leads
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                    {leads.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Ready for order creation
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>
          </Grid>

          {/* Converted Leads */}
          <AnimatedCard delay={0.5} sx={{ mb: 4 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Converted Leads ({leads.length})
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Ready to create order reviews
                </Typography>
              </Box>

              <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: 'grey.50' }}>
                      <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Phone</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Budget</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Occasion</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Jewelry Type</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {leads.map((lead, index) => (
                      <motion.tr
                        key={lead.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 + index * 0.05, duration: 0.3 }}
                        component={TableRow}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'grey.50',
                          },
                        }}
                      >
                        <TableCell>{lead.name}</TableCell>
                        <TableCell>{lead.phone}</TableCell>
                        <TableCell>₹{lead.budget.toLocaleString()}</TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {lead.occasion.replace('_', ' ')}
                        </TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {lead.jewelry_categories}
                        </TableCell>
                        <TableCell>
                          <AnimatedButton
                            variant="contained"
                            size="small"
                            startIcon={<Add />}
                            onClick={() => {
                              setSelectedLead(lead);
                              setCreateOrderDialogOpen(true);
                            }}
                          >
                            Create Order
                          </AnimatedButton>
                        </TableCell>
                      </motion.tr>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {leads.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No converted leads available
                  </Typography>
                </Box>
              )}
            </CardContent>
          </AnimatedCard>
          {/* Order Reviews */}
          <AnimatedCard delay={0.6} sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Order Reviews ({orderReviews.length})
              </Typography>

              <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: 'grey.50' }}>
                      <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Budget</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Occasion</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {orderReviews.map((review, index) => (
                      <motion.tr
                        key={review.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 + index * 0.05, duration: 0.3 }}
                        component={TableRow}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'grey.50',
                          },
                        }}
                      >
                        <TableCell>{review.lead.name}</TableCell>
                        <TableCell>₹{review.lead.budget.toLocaleString()}</TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {review.lead.occasion.replace('_', ' ')}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={review.approved ? 'Approved' : 'Pending'}
                            color={review.approved ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {new Date(review.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {!review.approved && (
                            <AnimatedButton
                              variant="contained"
                              size="small"
                              color="success"
                              startIcon={<CheckCircle />}
                              onClick={() => handleApproveOrder(review.id)}
                            >
                              Approve
                            </AnimatedButton>
                          )}
                        </TableCell>
                      </motion.tr>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {orderReviews.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No order reviews available
                  </Typography>
                </Box>
              )}
            </CardContent>
          </AnimatedCard>

          {/* Confirmed Orders */}
          <AnimatedCard delay={0.7}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Confirmed Orders ({confirmedOrders.length})
              </Typography>

              <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: 'grey.50' }}>
                      <TableCell sx={{ fontWeight: 600 }}>Customer</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Budget</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Jewelry Type</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>CAD Status</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {confirmedOrders.map((order, index) => (
                      <motion.tr
                        key={order.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8 + index * 0.05, duration: 0.3 }}
                        component={TableRow}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'grey.50',
                          },
                        }}
                      >
                        <TableCell>{order.lead.name}</TableCell>
                        <TableCell>₹{order.lead.budget.toLocaleString()}</TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {order.lead.jewelry_categories}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={order.cad_file ? 'Uploaded' : 'Pending'}
                            color={order.cad_file ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {new Date(order.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {order.cad_file ? (
                              <AnimatedButton
                                variant="outlined"
                                size="small"
                                startIcon={<Visibility />}
                                onClick={() => window.open(order.cad_file, '_blank')}
                              >
                                View CAD
                              </AnimatedButton>
                            ) : (
                              <>
                                <input
                                  id={`cad-upload-${order.id}`}
                                  type="file"
                                  accept=".dwg,.dxf,.step,.iges,.3dm"
                                  style={{ display: 'none' }}
                                  onChange={(e) => {
                                    if (e.target.files && e.target.files[0]) {
                                      handleCadUpload(order.id, e.target.files[0]);
                                    }
                                  }}
                                />
                                <AnimatedButton
                                  variant="contained"
                                  size="small"
                                  startIcon={<CloudUpload />}
                                  loading={uploadingCad === order.id}
                                  onClick={() => document.getElementById(`cad-upload-${order.id}`)?.click()}
                                >
                                  Upload CAD
                                </AnimatedButton>
                              </>
                            )}
                          </Box>
                        </TableCell>
                      </motion.tr>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {confirmedOrders.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No confirmed orders available
                  </Typography>
                </Box>
              )}
            </CardContent>
          </AnimatedCard>
        </StaggeredFadeIn>
      </Container>

      {/* Create Order Dialog */}
      <Dialog
        open={createOrderDialogOpen}
        onClose={() => setCreateOrderDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create Order Review</DialogTitle>
        <DialogContent>
          {selectedLead && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Create an order review for <strong>{selectedLead.name}</strong>?
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Budget: ₹{selectedLead.budget.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Occasion: {selectedLead.occasion.replace('_', ' ')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jewelry Type: {selectedLead.jewelry_categories}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateOrderDialogOpen(false)}>Cancel</Button>
          <AnimatedButton
            onClick={handleCreateOrderReview}
            variant="contained"
          >
            Create Order Review
          </AnimatedButton>
        </DialogActions>
      </Dialog>
    </PageTransition>
  );
};

export default ProductionPage;
