import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Collapse,
  Button,
  Divider,
} from '@mui/material';
import {
  FilterList,
  ExpandMore,
  ExpandLess,
  TrendingUp,
  People,
  AttachMoney,
  Star,
  Refresh,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import PageTransition from '../components/animations/PageTransition';
import StaggeredFadeIn from '../components/animations/StaggeredFadeIn';
import AnimatedButton from '../components/common/AnimatedButton';
import AnimatedCard from '../components/common/AnimatedCard';
import { getLeads, updateLeadStatus } from '../services/api';

// Type definitions
interface Lead {
  id: number;
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  status: string;
  remarks: string;
  created_at: string;
  updated_at: string;
  design_reference: string | null;
  jewelry_categories: string;
  store: string;
  catalogue_selection?: any[] | null;
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

interface LeadFilters {
  status: string;
  occasion: string;
  store: string;
  jewelry_type: string;
  minBudget: string;
  maxBudget: string;
}

interface FunnelDataItem {
  status: string;
  count: number;
  color: string;
  percentage: number;
}

interface InsightsData {
  occasionBreakdown: Record<string, number>;
  totalBudget: number;
  leadCount: number;
  premiumLeads: number;
  conversionRate: number;
  monthlyGrowth: number;
}

const MarketingPage: React.FC = () => {
  const navigate = useNavigate();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<LeadFilters>({
    status: '',
    occasion: '',
    store: '',
    jewelry_type: '',
    minBudget: '',
    maxBudget: '',
  });

  useEffect(() => {
    fetchLeads();
  }, [filters]);

  const fetchLeads = async () => {
    setLoading(true);
    setError(null);
    try {
      const params: any = {};
      if (filters.status) params.status = filters.status;
      if (filters.occasion) params.occasion = filters.occasion;
      if (filters.store) params.store = filters.store;
      if (filters.jewelry_type) params.jewelry_categories = filters.jewelry_type;
      if (filters.minBudget) params.min_budget = filters.minBudget;
      if (filters.maxBudget) params.max_budget = filters.maxBudget;

      console.log('Fetching leads with params:', params);
      const response = await getLeads(params);
      console.log('Leads response:', response);

      const leadsData = response.results || response || [];
      setLeads(Array.isArray(leadsData) ? leadsData : []);

      if (leadsData.length === 0) {
        console.log('No leads found');
      }
    } catch (error: any) {
      console.error('Error fetching leads:', error);
      setError(error.response?.data?.message || error.message || 'Failed to fetch leads');
      setLeads([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: keyof LeadFilters, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const resetFilters = () => {
    setFilters({
      status: '',
      occasion: '',
      store: '',
      jewelry_type: '',
      minBudget: '',
      maxBudget: '',
    });
  };

  const handleStatusUpdate = async (leadId: number, newStatus: string) => {
    try {
      await updateLeadStatus(leadId, newStatus);
      fetchLeads(); // Refresh the data
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'primary';
      case 'contacted': return 'secondary';
      case 'warm': return 'warning';
      case 'converted': return 'success';
      case 'dropped': return 'error';
      case 'order': return 'info';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    return status === 'new' ? 'Just In' : status.charAt(0).toUpperCase() + status.slice(1);
  };

  const calculateFunnelData = (): FunnelDataItem[] => {
    const statusCounts: Record<string, number> = {};
    leads.forEach((lead) => {
      const status = lead.status === 'new' ? 'Just In' : lead.status.charAt(0).toUpperCase() + lead.status.slice(1);
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    const funnelSteps = ['Just In', 'Contacted', 'Warm', 'Converted', 'Order'];
    const statusColors: Record<string, string> = {
      'Just In': '#3498db',
      Contacted: '#5dade2',
      Warm: '#e67e22',
      Converted: '#2ecc71',
      Order: '#9b59b6',
    };

    return funnelSteps.map((step) => ({
      status: step,
      count: statusCounts[step] || 0,
      color: statusColors[step] || '#95a5a6',
      percentage: leads.length > 0 ? ((statusCounts[step] || 0) / leads.length) * 100 : 0,
    }));
  };

  const calculateInsights = (): InsightsData => {
    const occasionBreakdown: Record<string, number> = {};
    let totalBudget = 0;
    let premiumLeads = 0;

    leads.forEach((lead) => {
      occasionBreakdown[lead.occasion] = (occasionBreakdown[lead.occasion] || 0) + 1;
      totalBudget += lead.budget;
      if (lead.budget >= 50000) premiumLeads++;
    });

    const convertedLeads = leads.filter(lead => lead.status === 'converted' || lead.status === 'order').length;
    const conversionRate = leads.length > 0 ? (convertedLeads / leads.length) * 100 : 0;

    return {
      occasionBreakdown,
      totalBudget,
      leadCount: leads.length,
      premiumLeads,
      conversionRate,
      monthlyGrowth: 12.5, // This would come from historical data
    };
  };

  const funnelData = calculateFunnelData();
  const insights = calculateInsights();

  return (
    <PageTransition>
      <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
        <StaggeredFadeIn>
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #FF6B6B 30%, #4ECDC4 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2rem', md: '3rem' },
              }}
            >
              Marketing Dashboard
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontSize: { xs: '1rem', md: '1.25rem' } }}>
              Analyze leads, track conversions, and optimize marketing strategies
            </Typography>
          </Box>

          {/* Insights Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <AnimatedCard delay={0.1}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <People sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Total Leads
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main' }}>
                    {insights.leadCount}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    +{insights.monthlyGrowth}% from last month
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <AnimatedCard delay={0.2}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AttachMoney sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Total Value
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'success.main' }}>
                    ₹{(insights.totalBudget / 100000).toFixed(1)}L
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pipeline value
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <AnimatedCard delay={0.3}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TrendingUp sx={{ color: 'warning.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Conversion Rate
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    {insights.conversionRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Lead to order conversion
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <AnimatedCard delay={0.4}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Star sx={{ color: 'secondary.main', mr: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Premium Leads
                    </Typography>
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                    {insights.premiumLeads}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Budget ≥ ₹50,000
                  </Typography>
                </CardContent>
              </AnimatedCard>
            </Grid>
          </Grid>

          {/* Filters Section */}
          <AnimatedCard delay={0.5} sx={{ mb: 4 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Filters
                </Typography>
                <Box>
                  <AnimatedButton
                    variant="outlined"
                    onClick={resetFilters}
                    startIcon={<Refresh />}
                    sx={{ mr: 1 }}
                  >
                    Reset
                  </AnimatedButton>
                  <IconButton onClick={() => setFiltersOpen(!filtersOpen)}>
                    {filtersOpen ? <ExpandLess /> : <ExpandMore />}
                  </IconButton>
                </Box>
              </Box>

              <AnimatePresence>
                {filtersOpen && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Divider sx={{ mb: 3 }} />
                    <Grid container spacing={3}>
                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Status</InputLabel>
                          <Select
                            value={filters.status}
                            label="Status"
                            onChange={(e) => handleFilterChange('status', e.target.value)}
                          >
                            <MenuItem value="">All</MenuItem>
                            <MenuItem value="new">Just In</MenuItem>
                            <MenuItem value="contacted">Contacted</MenuItem>
                            <MenuItem value="warm">Warm</MenuItem>
                            <MenuItem value="converted">Converted</MenuItem>
                            <MenuItem value="dropped">Dropped</MenuItem>
                            <MenuItem value="order">Order</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Occasion</InputLabel>
                          <Select
                            value={filters.occasion}
                            label="Occasion"
                            onChange={(e) => handleFilterChange('occasion', e.target.value)}
                          >
                            <MenuItem value="">All</MenuItem>
                            <MenuItem value="wedding">Wedding</MenuItem>
                            <MenuItem value="special_occasions">Special occasions</MenuItem>
                            <MenuItem value="casual">Casual</MenuItem>
                            <MenuItem value="other">Other</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Store</InputLabel>
                          <Select
                            value={filters.store}
                            label="Store"
                            onChange={(e) => handleFilterChange('store', e.target.value)}
                          >
                            <MenuItem value="">All</MenuItem>
                            <MenuItem value="khan_market">Khan Market</MenuItem>
                            <MenuItem value="amrawatta">Amrawatta</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Jewelry Type</InputLabel>
                          <Select
                            value={filters.jewelry_type}
                            label="Jewelry Type"
                            onChange={(e) => handleFilterChange('jewelry_type', e.target.value)}
                          >
                            <MenuItem value="">All</MenuItem>
                            <MenuItem value="necklace">Necklace</MenuItem>
                            <MenuItem value="bangles">Bangles</MenuItem>
                            <MenuItem value="earrings">Earrings</MenuItem>
                            <MenuItem value="rings">Rings</MenuItem>
                            <MenuItem value="bracelets">Bracelets</MenuItem>
                            <MenuItem value="pendants">Pendants</MenuItem>
                            <MenuItem value="chains">Chains</MenuItem>
                            <MenuItem value="anklets">Anklets</MenuItem>
                            <MenuItem value="nose_pins">Nose Pins</MenuItem>
                            <MenuItem value="toe_rings">Toe Rings</MenuItem>
                            <MenuItem value="maang_tikka">Maang Tikka</MenuItem>
                            <MenuItem value="sets">Jewelry Sets</MenuItem>
                            <MenuItem value="other">Other</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <TextField
                          fullWidth
                          size="small"
                          label="Min Budget"
                          type="number"
                          value={filters.minBudget}
                          onChange={(e) => handleFilterChange('minBudget', e.target.value)}
                        />
                      </Grid>

                      <Grid size={{ xs: 12, sm: 6, md: 2 }}>
                        <TextField
                          fullWidth
                          size="small"
                          label="Max Budget"
                          type="number"
                          value={filters.maxBudget}
                          onChange={(e) => handleFilterChange('maxBudget', e.target.value)}
                        />
                      </Grid>
                    </Grid>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardContent>
          </AnimatedCard>

          {/* Funnel Visualization */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <AnimatedCard delay={0.6}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Sales Funnel
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {funnelData.map((item, index) => (
                      <motion.div
                        key={item.status}
                        initial={{ width: 0, opacity: 0 }}
                        animate={{ width: '100%', opacity: 1 }}
                        transition={{ delay: 0.7 + index * 0.1, duration: 0.5 }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Typography variant="body2" sx={{ minWidth: 100, fontWeight: 500 }}>
                            {item.status}
                          </Typography>
                          <Box sx={{ flex: 1, mx: 2 }}>
                            <Box
                              sx={{
                                height: 8,
                                backgroundColor: 'grey.200',
                                borderRadius: 4,
                                overflow: 'hidden',
                              }}
                            >
                              <motion.div
                                style={{
                                  height: '100%',
                                  backgroundColor: item.color,
                                  borderRadius: 4,
                                }}
                                initial={{ width: 0 }}
                                animate={{ width: `${item.percentage}%` }}
                                transition={{ delay: 0.8 + index * 0.1, duration: 0.8 }}
                              />
                            </Box>
                          </Box>
                          <Typography variant="body2" sx={{ minWidth: 60, textAlign: 'right' }}>
                            {item.count} ({item.percentage.toFixed(1)}%)
                          </Typography>
                        </Box>
                      </motion.div>
                    ))}
                  </Box>
                </CardContent>
              </AnimatedCard>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <AnimatedCard delay={0.7}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Occasion Breakdown
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {Object.entries(insights.occasionBreakdown).map(([occasion, count], index) => {
                      const percentage = insights.leadCount > 0 ? (count / insights.leadCount) * 100 : 0;
                      const occasionColors = {
                        wedding: '#e74c3c',
                        special_occasions: '#9b59b6',
                        casual: '#3498db',
                        other: '#95a5a6',
                      };
                      const color = occasionColors[occasion as keyof typeof occasionColors] || '#95a5a6';

                      return (
                        <motion.div
                          key={occasion}
                          initial={{ width: 0, opacity: 0 }}
                          animate={{ width: '100%', opacity: 1 }}
                          transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Typography variant="body2" sx={{ minWidth: 120, fontWeight: 500, textTransform: 'capitalize' }}>
                              {occasion.replace('_', ' ')}
                            </Typography>
                            <Box sx={{ flex: 1, mx: 2 }}>
                              <Box
                                sx={{
                                  height: 8,
                                  backgroundColor: 'grey.200',
                                  borderRadius: 4,
                                  overflow: 'hidden',
                                }}
                              >
                                <motion.div
                                  style={{
                                    height: '100%',
                                    backgroundColor: color,
                                    borderRadius: 4,
                                  }}
                                  initial={{ width: 0 }}
                                  animate={{ width: `${percentage}%` }}
                                  transition={{ delay: 0.9 + index * 0.1, duration: 0.8 }}
                                />
                              </Box>
                            </Box>
                            <Typography variant="body2" sx={{ minWidth: 60, textAlign: 'right' }}>
                              {count} ({percentage.toFixed(1)}%)
                            </Typography>
                          </Box>
                        </motion.div>
                      );
                    })}
                  </Box>
                </CardContent>
              </AnimatedCard>
            </Grid>
          </Grid>

          {/* Leads Table */}
          <AnimatedCard delay={0.8}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Leads ({leads.length})
                </Typography>
                <AnimatedButton
                  variant="outlined"
                  onClick={fetchLeads}
                  startIcon={<Refresh />}
                  loading={loading}
                >
                  Refresh
                </AnimatedButton>
              </Box>

              <TableContainer component={Paper} sx={{ borderRadius: 2, overflow: 'hidden' }}>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: 'grey.50' }}>
                      <TableCell sx={{ fontWeight: 600 }}>
                        Customer
                        <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary', fontStyle: 'italic' }}>
                          Click row to view details
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Phone</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Budget</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Occasion</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Store</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Jewelry Type</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Created</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {leads.map((lead, index) => (
                      <TableRow
                        key={lead.id}
                        onClick={(e) => {
                          e.preventDefault();
                          console.log('Navigating to lead:', lead.id);
                          navigate(`/lead/${lead.id}`);
                        }}
                        sx={{
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            backgroundColor: 'primary.50',
                            transform: 'scale(1.01)',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          },
                        }}
                      >
                        <TableCell>{lead.name || 'N/A'}</TableCell>
                        <TableCell>{lead.phone || 'N/A'}</TableCell>
                        <TableCell>₹{lead.budget?.toLocaleString() || '0'}</TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {lead.occasion ? lead.occasion.replace('_', ' ') : 'N/A'}
                        </TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {lead.store ? lead.store.replace('_', ' ') : 'N/A'}
                        </TableCell>
                        <TableCell sx={{ textTransform: 'capitalize' }}>
                          {lead.jewelry_categories || 'N/A'}
                        </TableCell>
                        <TableCell onClick={(e) => e.stopPropagation()}>
                          <FormControl size="small" sx={{ minWidth: 120 }}>
                            <Select
                              value={lead.status}
                              onChange={(e) => handleStatusUpdate(lead.id, e.target.value)}
                              variant="outlined"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MenuItem value="new">Just In</MenuItem>
                              <MenuItem value="contacted">Contacted</MenuItem>
                              <MenuItem value="warm">Warm</MenuItem>
                              <MenuItem value="converted">Converted</MenuItem>
                              <MenuItem value="dropped">Dropped</MenuItem>
                              <MenuItem value="order">Order</MenuItem>
                            </Select>
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          {new Date(lead.created_at).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {error && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="error">
                    Error: {error}
                  </Typography>
                </Box>
              )}

              {leads.length === 0 && !loading && !error && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No leads found matching your filters
                  </Typography>
                </Box>
              )}
            </CardContent>
          </AnimatedCard>
        </StaggeredFadeIn>
      </Container>
    </PageTransition>
  );
};

export default MarketingPage;
