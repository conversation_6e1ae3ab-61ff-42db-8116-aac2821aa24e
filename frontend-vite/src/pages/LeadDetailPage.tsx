import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Divider,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack,
  Person,
  Phone,
  Store,
  Diamond,
  Event,
  CurrencyRupee,
  Schedule,
  Notes,
  CheckCircle,
  Cancel,
  Percent,
  Grade,
  Badge,
  WhatsApp,
  Email,
  Edit,
  History,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import PageTransition from '../components/animations/PageTransition';
import StaggeredFadeIn from '../components/animations/StaggeredFadeIn';
import AnimatedCard from '../components/common/AnimatedCard';
import WhatsAppMessaging from '../components/messaging/WhatsAppMessaging';
import { getLeadById } from '../services/api';

// Lead interface definition
interface Lead {
  id: number;
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  status: string;
  remarks: string;
  created_at: string;
  updated_at: string;
  design_reference: string | null;
  jewelry_categories: string;
  store: string;
  catalogue_selection?: any[] | null;
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

const LeadDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showWhatsApp, setShowWhatsApp] = useState(false);

  useEffect(() => {
    if (id) {
      fetchLead(id);
    }
  }, [id]);

  const fetchLead = async (leadId: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await getLeadById(leadId);
      setLead(response);
    } catch (error: any) {
      console.error('Error fetching lead:', error);
      setError(error.response?.data?.message || 'Failed to fetch lead details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      new: '#2196F3',
      contacted: '#FF9800',
      warm: '#4CAF50',
      converted: '#8BC34A',
      dropped: '#F44336',
      order: '#9C27B0',
    };
    return colors[status] || '#757575';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      new: 'Just In',
      contacted: 'Contacted',
      warm: 'Warm',
      converted: 'Converted',
      dropped: 'Dropped',
      order: 'Order',
    };
    return labels[status] || status;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <PageTransition>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
            <Typography variant="h6">Loading lead details...</Typography>
          </Box>
        </Container>
      </PageTransition>
    );
  }

  if (error || !lead) {
    return (
      <PageTransition>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h5" color="error" gutterBottom>
              {error || 'Lead not found'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<ArrowBack />}
              onClick={() => navigate('/marketing')}
              sx={{ mt: 2 }}
            >
              Back to Marketing Dashboard
            </Button>
          </Box>
        </Container>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
        <StaggeredFadeIn>
          {/* Header */}
          <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton
              onClick={() => navigate('/marketing')}
              sx={{
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' },
              }}
            >
              <ArrowBack />
            </IconButton>
            <Box sx={{ flexGrow: 1 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(45deg, #FF6B6B 30%, #4ECDC4 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontSize: { xs: '1.75rem', md: '2.125rem' },
                }}
              >
                {lead.name}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Lead ID: #{lead.id} • Created {formatDate(lead.created_at)}
              </Typography>
            </Box>
            <Chip
              label={getStatusLabel(lead.status)}
              sx={{
                bgcolor: getStatusColor(lead.status),
                color: 'white',
                fontWeight: 600,
                fontSize: '0.875rem',
                px: 2,
              }}
            />
          </Box>

          <Grid container spacing={3}>
            {/* Left Column - Lead Information */}
            <Grid size={{ xs: 12, lg: 8 }}>
              {/* Customer Information */}
              <AnimatedCard delay={0.1}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Person color="primary" />
                    Customer Information
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <Person />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Full Name
                          </Typography>
                          <Typography variant="body1" fontWeight={600}>
                            {lead.name}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <Phone />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Phone Number
                          </Typography>
                          <Typography variant="body1" fontWeight={600}>
                            {lead.phone}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Avatar sx={{ bgcolor: 'info.main' }}>
                          <Store />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Store Location
                          </Typography>
                          <Typography variant="body1" fontWeight={600} sx={{ textTransform: 'capitalize' }}>
                            {lead.store ? lead.store.replace('_', ' ') : 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Avatar sx={{ bgcolor: 'warning.main' }}>
                          <Badge />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Store Representative
                          </Typography>
                          <Typography variant="body1" fontWeight={600}>
                            {lead.store_representative || 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </AnimatedCard>

              {/* Product & Budget Information */}
              <AnimatedCard delay={0.2}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Diamond color="primary" />
                    Product & Budget Details
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <List dense>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <CurrencyRupee color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Budget"
                            secondary={formatCurrency(lead.budget)}
                            secondaryTypographyProps={{ fontWeight: 600, fontSize: '1.1rem' }}
                          />
                        </ListItem>

                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <Event color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Occasion"
                            secondary={lead.occasion ? lead.occasion.replace('_', ' ') : 'N/A'}
                            secondaryTypographyProps={{ fontWeight: 600, textTransform: 'capitalize' }}
                          />
                        </ListItem>
                      </List>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <List dense>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <Diamond color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Jewelry Category"
                            secondary={lead.jewelry_categories || 'N/A'}
                            secondaryTypographyProps={{ fontWeight: 600, textTransform: 'capitalize' }}
                          />
                        </ListItem>

                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <Schedule color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Timeline"
                            secondary={lead.timeline || 'N/A'}
                            secondaryTypographyProps={{ fontWeight: 600 }}
                          />
                        </ListItem>
                      </List>
                    </Grid>
                  </Grid>
                </CardContent>
              </AnimatedCard>

              {/* Customer Assessment */}
              <AnimatedCard delay={0.3}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Grade color="primary" />
                    Customer Assessment
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <List dense>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            {lead.previously_purchased ? <CheckCircle color="success" /> : <Cancel color="error" />}
                          </ListItemIcon>
                          <ListItemText
                            primary="Previously Purchased"
                            secondary={lead.previously_purchased ? 'Yes' : 'No'}
                            secondaryTypographyProps={{
                              fontWeight: 600,
                              color: lead.previously_purchased ? 'success.main' : 'error.main'
                            }}
                          />
                        </ListItem>

                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <Percent color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Conversion Probability"
                            secondary={`${lead.conversion_probability}%`}
                            secondaryTypographyProps={{ fontWeight: 600 }}
                          />
                        </ListItem>
                      </List>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <List dense>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon>
                            <Grade color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Retail Potential"
                            secondary={lead.retail_potential ? `${lead.retail_potential} Rating` : 'N/A'}
                            secondaryTypographyProps={{ fontWeight: 600 }}
                          />
                        </ListItem>
                      </List>
                    </Grid>
                  </Grid>
                </CardContent>
              </AnimatedCard>

              {/* Additional Information */}
              {lead.remarks && (
                <AnimatedCard delay={0.4}>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Notes color="primary" />
                      Remarks
                    </Typography>

                    <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
                      <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                        {lead.remarks}
                      </Typography>
                    </Paper>
                  </CardContent>
                </AnimatedCard>
              )}

              {/* Catalogue Selection */}
              {lead.catalogue_selection && lead.catalogue_selection.length > 0 && (
                <AnimatedCard delay={0.5}>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ mb: 3 }}>
                      Selected Catalogue Items
                    </Typography>

                    <Grid container spacing={2}>
                      {lead.catalogue_selection.map((item) => (
                        <Grid size={{ xs: 6, sm: 4 }} key={item.id}>
                          <Card sx={{ borderRadius: 2 }}>
                            <Box
                              component="img"
                              src={item.image}
                              alt={item.name || `Item ${item.id}`}
                              sx={{
                                width: '100%',
                                height: 120,
                                objectFit: 'cover',
                              }}
                            />
                            <CardContent sx={{ p: 1 }}>
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>
                                {item.name || `Item ${item.id}`}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </CardContent>
                </AnimatedCard>
              )}
            </Grid>

            {/* Right Column - Actions & Messaging */}
            <Grid size={{ xs: 12, lg: 4 }}>
              {/* Quick Actions */}
              <AnimatedCard delay={0.3}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    Quick Actions
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<WhatsApp />}
                      onClick={() => setShowWhatsApp(true)}
                      sx={{
                        bgcolor: '#25D366',
                        '&:hover': { bgcolor: '#128C7E' },
                        py: 1.5,
                      }}
                      fullWidth
                    >
                      WhatsApp Message
                    </Button>

                    <Button
                      variant="outlined"
                      startIcon={<Phone />}
                      href={`tel:${lead.phone}`}
                      fullWidth
                      sx={{ py: 1.5 }}
                    >
                      Call Customer
                    </Button>

                    <Button
                      variant="outlined"
                      startIcon={<Edit />}
                      onClick={() => navigate(`/lead/${lead.id}/edit`)}
                      fullWidth
                      sx={{ py: 1.5 }}
                    >
                      Edit Lead
                    </Button>
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* WhatsApp Messaging Component */}
              {showWhatsApp && (
                <WhatsAppMessaging
                  lead={lead}
                  onClose={() => setShowWhatsApp(false)}
                />
              )}
            </Grid>
          </Grid>
        </StaggeredFadeIn>
      </Container>
    </PageTransition>
  );
};

export default LeadDetailPage;
