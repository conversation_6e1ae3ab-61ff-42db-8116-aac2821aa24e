import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>ield,
  Box,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Paper,
  Grid,
  Checkbox,
  FormControlLabel,
  CardMedia,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  CloudUpload,
  Phone,
  Person,
  AttachMoney,
  Event,
  Store,
  Diamond,
  Notes,
  Schedule,
  CurrencyRupee,
  CheckCircle,
  Cancel,
  Percent,
  Grade,
  Badge,
  LocationOn,
  CalendarToday,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import PageTransition from '../components/animations/PageTransition';
import StaggeredFadeIn from '../components/animations/StaggeredFadeIn';
import AnimatedButton from '../components/common/AnimatedButton';
import AnimatedCard from '../components/common/AnimatedCard';
import WhatsAppNotification from '../components/notifications/WhatsAppNotification';
import { createLead, getCatalogueItems } from '../services/api';
import type { CatalogueItem } from '../types';

interface FormData {
  name: string;
  phone: string;
  address: string;
  budget: string;
  occasion: string;
  jewelry_categories: string;
  store: string;
  timeline: string;
  remarks: string;
  design_file: File | null;
  catalogue_items: number[];
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

const SalesPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    phone: '',
    address: '',
    budget: '',
    occasion: '',
    jewelry_categories: '',
    store: '',
    timeline: '',
    remarks: '',
    design_file: null,
    catalogue_items: [],
    previously_purchased: false,
    conversion_probability: 0,
    retail_potential: '',
    store_representative: '',
  });

  const [catalogueItems, setCatalogueItems] = useState<CatalogueItem[]>([]);
  const [selectedCatalogueIds, setSelectedCatalogueIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [alert, setAlert] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [showWhatsAppNotification, setShowWhatsAppNotification] = useState(false);
  const [submittedLeadData, setSubmittedLeadData] = useState<FormData | null>(null);

  useEffect(() => {
    fetchCatalogueItems();
  }, []);

  const fetchCatalogueItems = async () => {
    try {
      const response = await getCatalogueItems();
      setCatalogueItems(response.results || response);
    } catch (error) {
      console.error('Error fetching catalogue items:', error);
    }
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, design_file: file }));
    }
  };

  const handleCatalogueSelection = (itemId: number) => {
    setSelectedCatalogueIds(prev => {
      const newSelection = prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId];
      setFormData(prevForm => ({ ...prevForm, catalogue_items: newSelection }));
      return newSelection;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submitData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'catalogue_items') {
          value.forEach((id: number) => submitData.append('catalogue_ids', id.toString()));
        } else if (key === 'design_file' && value) {
          submitData.append(key, value);
        } else if (value !== null && value !== '') {
          submitData.append(key, value.toString());
        }
      });

      await createLead(submitData);
      setAlert({ type: 'success', message: 'Lead submitted successfully!' });

      // Store the submitted lead data for WhatsApp notification
      setSubmittedLeadData({ ...formData });

      // Show WhatsApp notification dialog
      setShowWhatsAppNotification(true);

      // Reset form
      setFormData({
        name: '',
        phone: '',
        address: '',
        budget: '',
        occasion: '',
        jewelry_categories: '',
        store: '',
        timeline: '',
        remarks: '',
        design_file: null,
        catalogue_items: [],
        previously_purchased: false,
        conversion_probability: 0,
        retail_potential: '',
        store_representative: '',
      });
      setSelectedCatalogueIds([]);
    } catch (error) {
      setAlert({ type: 'error', message: 'Failed to submit lead. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageTransition>
      <Container maxWidth="md" sx={{ py: { xs: 2, md: 4 } }}>
        <StaggeredFadeIn>
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2rem', md: '3rem' },
              }}
            >
              Sales Dashboard
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Create new leads and manage customer inquiries
            </Typography>
          </Box>

          <form onSubmit={handleSubmit}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {/* Store Location */}
              <AnimatedCard delay={0.1}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    🏪 Store Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <FormControl
                      fullWidth
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                    >
                      <InputLabel>Store Location</InputLabel>
                      <Select
                        value={formData.store}
                        label="Store Location"
                        onChange={(e) => handleInputChange('store', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Store color="primary" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="khan_market">Khan Market</MenuItem>
                        <MenuItem value="amrawatta">Amrawatta</MenuItem>
                      </Select>
                    </FormControl>

                    <TextField
                      fullWidth
                      label="Store Representative"
                      value={formData.store_representative}
                      onChange={(e) => handleInputChange('store_representative', e.target.value)}
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Badge color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Customer Information */}
              <AnimatedCard delay={0.2}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    👤 Customer Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <TextField
                      fullWidth
                      label="Customer Name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Phone Number"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      multiline
                      rows={3}
                      placeholder="Customer's address"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                            <LocationOn color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Product Details */}
              <AnimatedCard delay={0.3}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    💎 Product Details
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <FormControl
                      fullWidth
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                    >
                      <InputLabel>Jewelry Category</InputLabel>
                      <Select
                        value={formData.jewelry_categories}
                        label="Jewelry Category"
                        onChange={(e) => handleInputChange('jewelry_categories', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Diamond color="primary" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="necklace">Necklace</MenuItem>
                        <MenuItem value="bangles">Bangles</MenuItem>
                        <MenuItem value="earrings">Earrings</MenuItem>
                        <MenuItem value="rings">Rings</MenuItem>
                        <MenuItem value="bracelets">Bracelets</MenuItem>
                        <MenuItem value="pendants">Pendants</MenuItem>
                        <MenuItem value="chains">Chains</MenuItem>
                        <MenuItem value="anklets">Anklets</MenuItem>
                        <MenuItem value="nose_pins">Nose Pins</MenuItem>
                        <MenuItem value="toe_rings">Toe Rings</MenuItem>
                        <MenuItem value="maang_tikka">Maang Tikka</MenuItem>
                        <MenuItem value="sets">Jewelry Sets</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl
                      fullWidth
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                    >
                      <InputLabel>Occasion</InputLabel>
                      <Select
                        value={formData.occasion}
                        label="Occasion"
                        onChange={(e) => handleInputChange('occasion', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Event color="primary" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="wedding">Wedding</MenuItem>
                        <MenuItem value="special_occasions">Special occasions</MenuItem>
                        <MenuItem value="casual">Casual</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Budget Information */}
              <AnimatedCard delay={0.4}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    💰 Budget Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <TextField
                      fullWidth
                      label="Budget (₹)"
                      value={formData.budget}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Allow only numbers and empty string
                        if (value === '' || /^\d+$/.test(value)) {
                          handleInputChange('budget', value);
                        }
                      }}
                      required
                      placeholder="Enter budget amount"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CurrencyRupee color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Customer Assessment */}
              <AnimatedCard delay={0.5}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    📊 Customer Assessment
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <FormControl
                      fullWidth
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                    >
                      <InputLabel>Previously Purchased from Symetree</InputLabel>
                      <Select
                        value={formData.previously_purchased ? 'yes' : 'no'}
                        label="Previously Purchased from Symetree"
                        onChange={(e) => handleInputChange('previously_purchased', e.target.value === 'yes')}
                        startAdornment={
                          <InputAdornment position="start">
                            {formData.previously_purchased ? <CheckCircle color="primary" /> : <Cancel color="primary" />}
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Select>
                    </FormControl>

                    <TextField
                      fullWidth
                      label="Conversion Probability (%)"
                      type="number"
                      value={formData.conversion_probability}
                      onChange={(e) => handleInputChange('conversion_probability', Math.min(100, Math.max(0, Number(e.target.value))))}
                      inputProps={{ min: 0, max: 100 }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Percent color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <FormControl
                      fullWidth
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                    >
                      <InputLabel>Retail Potential Buyer</InputLabel>
                      <Select
                        value={formData.retail_potential}
                        label="Retail Potential Buyer"
                        onChange={(e) => handleInputChange('retail_potential', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Grade color="primary" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="A">A - Excellent</MenuItem>
                        <MenuItem value="B">B - Good</MenuItem>
                        <MenuItem value="C">C - Average</MenuItem>
                        <MenuItem value="D">D - Below Average</MenuItem>
                        <MenuItem value="E">E - Poor</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Timeline and Remarks */}
              <AnimatedCard delay={0.6}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    📝 Additional Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <TextField
                      fullWidth
                      label="Timeline (Expected Date)"
                      type="date"
                      value={formData.timeline}
                      onChange={(e) => handleInputChange('timeline', e.target.value)}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarToday color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Remarks"
                      multiline
                      rows={4}
                      value={formData.remarks}
                      onChange={(e) => handleInputChange('remarks', e.target.value)}
                      placeholder="Additional notes or requirements"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                            <Notes color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </CardContent>
              </AnimatedCard>

              {/* Design File Upload */}
              <AnimatedCard delay={0.7}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    🎨 Design Reference
                  </Typography>

                  <Paper
                    sx={{
                      p: 4,
                      border: '2px dashed #e0e0e0',
                      borderRadius: 2,
                      textAlign: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: 'primary.50',
                      },
                    }}
                    onClick={() => document.getElementById('design-file-input')?.click()}
                  >
                    <input
                      id="design-file-input"
                      type="file"
                      accept="image/*,.pdf"
                      style={{ display: 'none' }}
                      onChange={handleFileUpload}
                    />
                    <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      Upload Design Reference
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formData.design_file
                        ? `Selected: ${formData.design_file.name}`
                        : 'Drag and drop an image file here, or click to select'
                      }
                    </Typography>
                  </Paper>
                </CardContent>
              </AnimatedCard>

              {/* Catalogue Selection */}
              <AnimatedCard delay={0.8}>
                <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontSize: { xs: '1.25rem', md: '1.5rem' },
                    }}
                  >
                    📚 Catalogue Selection
                  </Typography>

                  {catalogueItems.length > 0 ? (
                    <Grid container spacing={2}>
                      {catalogueItems.map((item) => (
                        <Grid size={{ xs: 6, sm: 4, md: 3 }} key={item.id}>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Card
                              sx={{
                                cursor: 'pointer',
                                border: selectedCatalogueIds.includes(item.id)
                                  ? '2px solid #2196F3'
                                  : '2px solid transparent',
                                transition: 'all 0.3s ease',
                                borderRadius: 2,
                              }}
                              onClick={() => handleCatalogueSelection(item.id)}
                            >
                              <CardMedia
                                component="img"
                                height="120"
                                image={item.image}
                                alt={item.name || `Catalogue item ${item.id}`}
                                sx={{ objectFit: 'cover' }}
                              />
                              <CardContent sx={{ p: 1 }}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={selectedCatalogueIds.includes(item.id)}
                                      onChange={() => handleCatalogueSelection(item.id)}
                                    />
                                  }
                                  label={item.name || `Item ${item.id}`}
                                  sx={{ m: 0 }}
                                />
                              </CardContent>
                            </Card>
                          </motion.div>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No catalogue items available
                    </Typography>
                  )}
                </CardContent>
              </AnimatedCard>

              {/* Submit Button */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <AnimatedButton
                  type="submit"
                  variant="contained"
                  size="large"
                  loading={isSubmitting}
                  fullWidth
                  sx={{
                    py: 2,
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    boxShadow: '0 4px 20px rgba(33, 150, 243, 0.3)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                      boxShadow: '0 6px 25px rgba(33, 150, 243, 0.4)',
                    },
                    maxWidth: { xs: '100%', sm: 400 },
                  }}
                >
                  🚀 Submit Lead
                </AnimatedButton>
              </Box>
            </Box>
          </form>
        </StaggeredFadeIn>
      </Container>

      <Snackbar
        open={!!alert}
        autoHideDuration={6000}
        onClose={() => setAlert(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlert(null)}
          severity={alert?.type}
          sx={{ width: '100%' }}
        >
          {alert?.message}
        </Alert>
      </Snackbar>

      {/* WhatsApp Notification Dialog */}
      {submittedLeadData && (
        <WhatsAppNotification
          open={showWhatsAppNotification}
          onClose={() => setShowWhatsAppNotification(false)}
          leadData={submittedLeadData}
          onNotificationSent={() => {
            console.log('WhatsApp notifications sent successfully');
          }}
        />
      )}
    </PageTransition>
  );
};

export default SalesPage;
