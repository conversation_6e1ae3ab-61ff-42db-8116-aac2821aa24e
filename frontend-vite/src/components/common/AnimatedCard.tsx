import React from 'react';
import { Card } from '@mui/material';
import { motion } from 'framer-motion';
import { styled } from '@mui/material/styles';

interface AnimatedCardProps {
  children: React.ReactNode;
  hoverEffect?: boolean;
  delay?: number;
  sx?: any;
  className?: string;
  elevation?: number;
}

const MotionCard = motion.create(Card);

const StyledCard = styled(MotionCard)(({ theme }) => ({
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  hoverEffect = true,
  delay = 0,
  sx,
  className,
  elevation = 1,
}) => {
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        delay,
      },
    },
  };

  const hoverVariants = hoverEffect
    ? {
        whileHover: {
          y: -4,
          transition: { type: 'spring', stiffness: 400, damping: 17 },
        },
        whileTap: { scale: 0.98 },
      }
    : {};

  return (
    <StyledCard
      sx={sx}
      className={className}
      elevation={elevation}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      {...hoverVariants}
    >
      {children}
    </StyledCard>
  );
};

export default AnimatedCard;
