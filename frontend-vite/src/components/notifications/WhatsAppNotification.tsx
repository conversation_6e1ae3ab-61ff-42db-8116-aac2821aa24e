import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
} from '@mui/material';
import {
  WhatsApp,
  CheckCircle,
  Error,
  Send,
  Phone,
  Store,
  Diamond,
  Event,
  CurrencyRupee,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { whatsappService } from '../../services/whatsappService';

interface WhatsAppNotificationProps {
  open: boolean;
  onClose: () => void;
  leadData: {
    name: string;
    phone: string;
    store: string;
    jewelry_categories: string;
    budget: number;
    occasion: string;
  };
  onNotificationSent?: () => void;
}

interface NotificationStatus {
  phoneNumber: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
}

const NOTIFICATION_RECIPIENTS = [
  { number: '+91 7732847694', name: 'Manager 1' },
  { number: '+91 90010 13571', name: 'Manager 2' },
  { number: '+91 98290 68777', name: 'Manager 3' },
];

const WhatsAppNotification: React.FC<WhatsAppNotificationProps> = ({
  open,
  onClose,
  leadData,
  onNotificationSent,
}) => {
  const [sending, setSending] = useState(false);
  const [sent, setSent] = useState(false);
  const [notificationStatuses, setNotificationStatuses] = useState<NotificationStatus[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleSendNotifications = async () => {
    setSending(true);
    setError(null);
    setSent(false);

    // Initialize status for all recipients
    const initialStatuses: NotificationStatus[] = NOTIFICATION_RECIPIENTS.map(recipient => ({
      phoneNumber: recipient.number,
      status: 'pending'
    }));
    setNotificationStatuses(initialStatuses);

    try {
      // Check if WhatsApp service is configured
      if (!whatsappService.isConfigured()) {
        throw new Error('WhatsApp service is not configured. Please check your Interakt API key.');
      }

      // Send notifications
      await whatsappService.sendLeadNotification(leadData);

      // Update all statuses to success (since sendLeadNotification doesn't throw for individual failures)
      const successStatuses: NotificationStatus[] = NOTIFICATION_RECIPIENTS.map(recipient => ({
        phoneNumber: recipient.number,
        status: 'success',
        message: 'Notification sent successfully'
      }));
      setNotificationStatuses(successStatuses);

      setSent(true);
      onNotificationSent?.();
    } catch (error: any) {
      console.error('Error sending WhatsApp notifications:', error);
      setError(error.message || 'Failed to send WhatsApp notifications');
      
      // Update all statuses to error
      const errorStatuses: NotificationStatus[] = NOTIFICATION_RECIPIENTS.map(recipient => ({
        phoneNumber: recipient.number,
        status: 'error',
        message: error.message || 'Failed to send notification'
      }));
      setNotificationStatuses(errorStatuses);
    } finally {
      setSending(false);
    }
  };

  const handleClose = () => {
    if (!sending) {
      onClose();
      // Reset state when closing
      setTimeout(() => {
        setSent(false);
        setNotificationStatuses([]);
        setError(null);
      }, 300);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'error':
        return <Error color="error" />;
      case 'pending':
      default:
        return <CircularProgress size={20} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'pending':
      default:
        return 'default';
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <WhatsApp sx={{ color: '#25D366' }} />
        WhatsApp Lead Notification
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Lead Details
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemIcon>
                <Store color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Store Location"
                secondary={leadData.store?.replace('_', ' ') || 'N/A'}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Phone color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Customer"
                secondary={`${leadData.name} (${leadData.phone})`}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Diamond color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Product Type"
                secondary={leadData.jewelry_categories?.replace('_', ' ') || 'N/A'}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <CurrencyRupee color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Budget"
                secondary={new Intl.NumberFormat('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                  minimumFractionDigits: 0,
                }).format(leadData.budget)}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Event color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Occasion"
                secondary={leadData.occasion?.replace('_', ' ') || 'N/A'}
              />
            </ListItem>
          </List>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Notification Recipients
          </Typography>
          
          <List dense>
            {NOTIFICATION_RECIPIENTS.map((recipient, index) => {
              const status = notificationStatuses.find(s => s.phoneNumber === recipient.number);
              
              return (
                <motion.div
                  key={recipient.number}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ListItem>
                    <ListItemIcon>
                      {status ? getStatusIcon(status.status) : <WhatsApp color="action" />}
                    </ListItemIcon>
                    <ListItemText
                      primary={recipient.name}
                      secondary={recipient.number}
                    />
                    {status && (
                      <Chip
                        label={status.status}
                        color={getStatusColor(status.status) as any}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </ListItem>
                </motion.div>
              );
            })}
          </List>
        </Box>

        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            </motion.div>
          )}

          {sent && !error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <Alert severity="success" sx={{ mb: 2 }}>
                WhatsApp notifications sent successfully to all recipients!
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={sending}>
          {sent ? 'Close' : 'Cancel'}
        </Button>
        
        {!sent && (
          <Button
            variant="contained"
            onClick={handleSendNotifications}
            disabled={sending}
            startIcon={sending ? <CircularProgress size={20} /> : <Send />}
            sx={{
              bgcolor: '#25D366',
              '&:hover': { bgcolor: '#128C7E' },
            }}
          >
            {sending ? 'Sending...' : 'Send Notifications'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default WhatsAppNotification;
