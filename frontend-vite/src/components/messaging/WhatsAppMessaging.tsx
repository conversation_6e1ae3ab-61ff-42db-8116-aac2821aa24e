import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Box,
  TextField,
  IconButton,
  Avatar,
  Paper,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Send,
  WhatsApp,
  Close,
  AttachFile,
  EmojiEmotions,
  MoreVert,
  CheckCircle,
  Schedule,
  Error,
  Refresh,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { whatsappService } from '../../services/whatsappService';

// Lead interface definition
interface Lead {
  id: number;
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  status: string;
  remarks: string;
  created_at: string;
  updated_at: string;
  design_reference: string | null;
  jewelry_categories: string;
  store: string;
  catalogue_selection?: any[] | null;
  previously_purchased: boolean;
  conversion_probability: number;
  retail_potential: string;
  store_representative: string;
}

interface WhatsAppMessage {
  id: string;
  content: string;
  timestamp: string;
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'image' | 'document';
  mediaUrl?: string;
}

interface WhatsAppTemplate {
  id: string;
  name: string;
  content: string;
  variables: string[];
}

interface WhatsAppMessagingProps {
  lead: Lead;
  onClose: () => void;
}

const WhatsAppMessaging: React.FC<WhatsAppMessagingProps> = ({ lead, onClose }) => {
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);
  const [templates, setTemplates] = useState<WhatsAppTemplate[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock templates - replace with actual API call
  const mockTemplates: WhatsAppTemplate[] = [
    {
      id: 'welcome',
      name: 'Welcome Message',
      content: 'Hello {{name}}, thank you for your interest in our jewelry collection. How can we assist you today?',
      variables: ['name'],
    },
    {
      id: 'follow_up',
      name: 'Follow Up',
      content: 'Hi {{name}}, I wanted to follow up on your inquiry about {{product}}. Do you have any questions?',
      variables: ['name', 'product'],
    },
    {
      id: 'appointment',
      name: 'Appointment Booking',
      content: 'Hello {{name}}, would you like to schedule an appointment to view our {{category}} collection at our {{store}} store?',
      variables: ['name', 'category', 'store'],
    },
  ];

  useEffect(() => {
    fetchMessages();
    setTemplates(mockTemplates);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchMessages = async () => {
    setLoading(true);
    try {
      if (whatsappService.isConfigured()) {
        // Fetch actual message history from Interakt
        const messageHistory = await whatsappService.getMessageHistory(lead.phone);

        // Convert Interakt messages to our format
        const convertedMessages: WhatsAppMessage[] = messageHistory.map((msg: any, index: number) => ({
          id: msg.id || index.toString(),
          content: msg.text || msg.message?.text || '',
          timestamp: msg.timestamp || new Date().toISOString(),
          direction: msg.direction === 'outbound' ? 'outbound' : 'inbound',
          status: msg.status || 'delivered',
          type: 'text',
        }));

        setMessages(convertedMessages);
      } else {
        // Fallback to mock messages if service not configured
        const mockMessages: WhatsAppMessage[] = [
          {
            id: '1',
            content: 'Hello, I\'m interested in your wedding jewelry collection.',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            direction: 'inbound',
            status: 'read',
            type: 'text',
          },
          {
            id: '2',
            content: 'Thank you for your interest! I\'d be happy to help you find the perfect pieces for your special day.',
            timestamp: new Date(Date.now() - 3000000).toISOString(),
            direction: 'outbound',
            status: 'read',
            type: 'text',
          },
        ];
        setMessages(mockMessages);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    setSending(true);
    const messageId = Date.now().toString();
    const message: WhatsAppMessage = {
      id: messageId,
      content: newMessage,
      timestamp: new Date().toISOString(),
      direction: 'outbound',
      status: 'sent',
      type: 'text',
    };

    // Optimistically add message
    setMessages(prev => [...prev, message]);
    const messageContent = newMessage;
    setNewMessage('');

    try {
      if (whatsappService.isConfigured()) {
        // Send actual WhatsApp message via Interakt
        await whatsappService.sendMessageToLead(lead.phone, messageContent);

        // Update message status to delivered
        setMessages(prev =>
          prev.map(msg =>
            msg.id === messageId
              ? { ...msg, status: 'delivered' }
              : msg
          )
        );
      } else {
        // Mock delay for demo purposes
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update message status
        setMessages(prev =>
          prev.map(msg =>
            msg.id === messageId
              ? { ...msg, status: 'delivered' }
              : msg
          )
        );
      }
    } catch (error: any) {
      console.error('Error sending message:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, status: 'failed' }
            : msg
        )
      );
      setError(error.message || 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const sendTemplate = async (template: WhatsAppTemplate) => {
    let content = template.content;

    // Replace variables with actual values
    content = content.replace('{{name}}', lead.name);
    content = content.replace('{{product}}', lead.jewelry_categories || 'jewelry');
    content = content.replace('{{category}}', lead.jewelry_categories || 'jewelry');
    content = content.replace('{{store}}', lead.store?.replace('_', ' ') || 'our');

    setNewMessage(content);
    setShowTemplates(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Schedule sx={{ fontSize: 16, color: 'grey.500' }} />;
      case 'delivered':
        return <CheckCircle sx={{ fontSize: 16, color: 'grey.500' }} />;
      case 'read':
        return <CheckCircle sx={{ fontSize: 16, color: 'primary.main' }} />;
      case 'failed':
        return <Error sx={{ fontSize: 16, color: 'error.main' }} />;
      default:
        return null;
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
    >
      <Card sx={{ height: 600, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: '#25D366' }}>
              <WhatsApp />
            </Avatar>
          }
          title={
            <Box>
              <Typography variant="h6">{lead.name}</Typography>
              <Typography variant="caption" color="text.secondary">
                {lead.phone}
              </Typography>
            </Box>
          }
          action={
            <Box>
              <IconButton onClick={() => setShowTemplates(true)}>
                <MoreVert />
              </IconButton>
              <IconButton onClick={onClose}>
                <Close />
              </IconButton>
            </Box>
          }
          sx={{ bgcolor: '#25D366', color: 'white' }}
        />

        {/* Messages */}
        <CardContent sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <ListItem
                      sx={{
                        display: 'flex',
                        justifyContent: message.direction === 'outbound' ? 'flex-end' : 'flex-start',
                        px: 1,
                        py: 0.5,
                      }}
                    >
                      <Paper
                        sx={{
                          p: 2,
                          maxWidth: '70%',
                          bgcolor: message.direction === 'outbound' ? '#DCF8C6' : 'white',
                          border: message.direction === 'inbound' ? '1px solid #e0e0e0' : 'none',
                        }}
                      >
                        <Typography variant="body2" sx={{ mb: 0.5 }}>
                          {message.content}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 0.5 }}>
                          <Typography variant="caption" color="text.secondary">
                            {formatTime(message.timestamp)}
                          </Typography>
                          {message.direction === 'outbound' && getStatusIcon(message.status)}
                        </Box>
                      </Paper>
                    </ListItem>
                  </motion.div>
                ))}
              </AnimatePresence>
              <div ref={messagesEndRef} />
            </List>
          )}
        </CardContent>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" onClose={() => setError(null)} sx={{ m: 1 }}>
            {error}
          </Alert>
        )}

        {/* Message Input */}
        <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              fullWidth
              multiline
              maxRows={3}
              placeholder="Type a message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              variant="outlined"
              size="small"
            />
            <IconButton
              color="primary"
              onClick={sendMessage}
              disabled={!newMessage.trim() || sending}
              sx={{ bgcolor: '#25D366', color: 'white', '&:hover': { bgcolor: '#128C7E' } }}
            >
              {sending ? <CircularProgress size={20} color="inherit" /> : <Send />}
            </IconButton>
          </Box>
        </Box>
      </Card>

      {/* Templates Dialog */}
      <Dialog open={showTemplates} onClose={() => setShowTemplates(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Message Templates</DialogTitle>
        <DialogContent>
          <List>
            {templates.map((template) => (
              <ListItem
                key={template.id}
                button
                onClick={() => sendTemplate(template)}
                sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 1 }}
              >
                <ListItemText
                  primary={template.name}
                  secondary={template.content}
                  secondaryTypographyProps={{ noWrap: true }}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplates(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default WhatsAppMessaging;
