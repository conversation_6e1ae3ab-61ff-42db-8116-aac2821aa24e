import axios from 'axios';

// Interakt API configuration
const INTERAKT_API_URL = 'https://api.interakt.ai/v1';
const INTERAKT_API_KEY = import.meta.env.VITE_INTERAKT_API_KEY || '';

// WhatsApp notification recipients
const NOTIFICATION_RECIPIENTS = [
  '+917732847694',
  '+919001013571',
  '+919829068777'
];

interface WhatsAppTemplateParams {
  store_location: string;
  customer_name: string;
  product_type: string;
  quoted_price: string;
  occasion: string;
}

interface InteraktMessagePayload {
  countryCode: string;
  phoneNumber: string;
  type: 'Template';
  template: {
    name: string;
    languageCode: string;
    headerValues?: string[];
    bodyValues: string[];
    buttonValues?: {
      [key: string]: string;
    };
  };
}

class WhatsAppService {
  private apiKey: string;
  private baseURL: string;

  constructor() {
    this.apiKey = INTERAKT_API_KEY;
    this.baseURL = INTERAKT_API_URL;
  }

  /**
   * Send WhatsApp template message via Interakt API
   */
  async sendTemplateMessage(
    phoneNumber: string,
    templateName: string,
    params: WhatsAppTemplateParams
  ): Promise<any> {
    try {
      // Parse phone number to extract country code and number
      const { countryCode, number } = this.parsePhoneNumber(phoneNumber);

      const payload: InteraktMessagePayload = {
        countryCode,
        phoneNumber: number,
        type: 'Template',
        template: {
          name: templateName,
          languageCode: 'en',
          bodyValues: [
            params.store_location,
            params.customer_name,
            params.product_type,
            params.quoted_price,
            params.occasion
          ]
        }
      };

      const response = await axios.post(
        `${this.baseURL}/public/message/`,
        payload,
        {
          headers: {
            'Authorization': `Basic ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('WhatsApp message sent successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error sending WhatsApp message:', error.response?.data || error.message);
      throw new Error(`Failed to send WhatsApp message: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Send lead notification to all configured recipients
   */
  async sendLeadNotification(leadData: {
    store: string;
    name: string;
    jewelry_categories: string;
    budget: number;
    occasion: string;
  }): Promise<void> {
    const templateParams: WhatsAppTemplateParams = {
      store_location: this.formatStoreName(leadData.store),
      customer_name: leadData.name,
      product_type: this.formatJewelryCategory(leadData.jewelry_categories),
      quoted_price: this.formatCurrency(leadData.budget),
      occasion: this.formatOccasion(leadData.occasion)
    };

    const promises = NOTIFICATION_RECIPIENTS.map(async (phoneNumber) => {
      try {
        await this.sendTemplateMessage(phoneNumber, 'lead_new', templateParams);
        console.log(`Notification sent to ${phoneNumber}`);
      } catch (error) {
        console.error(`Failed to send notification to ${phoneNumber}:`, error);
        // Don't throw error for individual failures, just log them
      }
    });

    // Wait for all notifications to complete (but don't fail if some fail)
    await Promise.allSettled(promises);
  }

  /**
   * Send WhatsApp message to a specific lead
   */
  async sendMessageToLead(
    phoneNumber: string,
    message: string
  ): Promise<any> {
    try {
      const { countryCode, number } = this.parsePhoneNumber(phoneNumber);

      const payload = {
        countryCode,
        phoneNumber: number,
        type: 'Text',
        message: {
          text: message
        }
      };

      const response = await axios.post(
        `${this.baseURL}/public/message/`,
        payload,
        {
          headers: {
            'Authorization': `Basic ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Error sending WhatsApp message to lead:', error.response?.data || error.message);
      throw new Error(`Failed to send message: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get message history for a phone number
   */
  async getMessageHistory(phoneNumber: string): Promise<any[]> {
    try {
      const { countryCode, number } = this.parsePhoneNumber(phoneNumber);

      const response = await axios.get(
        `${this.baseURL}/public/track/messages/`,
        {
          params: {
            countryCode,
            phoneNumber: number,
            limit: 50
          },
          headers: {
            'Authorization': `Basic ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.messages || [];
    } catch (error: any) {
      console.error('Error fetching message history:', error.response?.data || error.message);
      return [];
    }
  }

  /**
   * Parse phone number to extract country code and number
   */
  private parsePhoneNumber(phoneNumber: string): { countryCode: string; number: string } {
    // Remove any non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Handle Indian numbers
    if (cleaned.startsWith('91') && cleaned.length === 12) {
      return {
        countryCode: '91',
        number: cleaned.substring(2)
      };
    }

    // Handle numbers without country code (assume Indian)
    if (cleaned.length === 10) {
      return {
        countryCode: '91',
        number: cleaned
      };
    }

    // Default case
    return {
      countryCode: '91',
      number: cleaned.length > 10 ? cleaned.substring(cleaned.length - 10) : cleaned
    };
  }

  /**
   * Format store name for display
   */
  private formatStoreName(store: string): string {
    return store ? store.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Store';
  }

  /**
   * Format jewelry category for display
   */
  private formatJewelryCategory(category: string): string {
    return category ? category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Jewelry';
  }

  /**
   * Format currency amount
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Format occasion for display
   */
  private formatOccasion(occasion: string): string {
    return occasion ? occasion.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Special Occasion';
  }

  /**
   * Check if WhatsApp service is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }
}

export const whatsappService = new WhatsAppService();
export default WhatsAppService;
