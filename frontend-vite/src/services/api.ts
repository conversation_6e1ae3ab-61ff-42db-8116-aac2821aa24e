import axios from 'axios';

const API_URL = 'http://localhost:8000/api';

// Create API instance without token
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Token ${token}`;
  }
  return config;
});

// Authentication API
export const login = async (username: string, password: string) => {
  const response = await api.post('/login/', { username, password });
  return response.data;
};

export const getUserProfile = async () => {
  const response = await api.get('/profile/');
  return response.data;
};

// Leads API
export const getLeads = async (params?: any) => {
  // Create a new params object with pagination overrides
  const paginationOverride = {
    ...params,
    page_size: 100, // Request up to 100 items per page
    limit: 100     // Additional parameter to ensure we get all records
  };

  console.log('API request with params:', paginationOverride);
  const response = await api.get('/leads/', { params: paginationOverride });
  console.log('API response data:', response.data);
  console.log('Response count:', response.data?.count, 'Results length:', response.data?.results?.length);
  return response.data;
};

export const getLead = async (id: number) => {
  const response = await api.get(`/leads/${id}/`);
  return response.data;
};

export const getLeadById = async (id: string) => {
  const response = await api.get(`/leads/${id}/`);
  return response.data;
};

export const createLead = async (data: any) => {
  const formData = new FormData();

  // Add regular fields
  Object.keys(data).forEach((key) => {
    if (key !== 'design_file' && key !== 'catalogue_ids') {
      formData.append(key, data[key]);
    }
  });

  // Add file if present
  if (data.design_file) {
    formData.append('design_file', data.design_file);
  }

  // Add catalogue selections if present
  if (data.catalogue_ids && Array.isArray(data.catalogue_ids)) {
    data.catalogue_ids.forEach((id: number) => {
      formData.append('catalogue_ids', id.toString());
    });
  }

  const response = await api.post('/leads/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

export const updateLead = async (id: number, data: any) => {
  const response = await api.put(`/leads/${id}/`, data);
  return response.data;
};

export const updateLeadStatus = async (id: number, status: string) => {
  const response = await api.post(`/leads/${id}/update_status/`, { status });
  return response.data;
};

export const deleteLead = async (id: number) => {
  const response = await api.delete(`/leads/${id}/`);
  return response.data;
};

// Catalogue API
export const getCatalogueItems = async (params?: any) => {
  const response = await api.get('/catalogue/', { params });
  return response.data;
};

export const getCatalogueItem = async (id: number) => {
  const response = await api.get(`/catalogue/${id}/`);
  return response.data;
};

// Orders API
export const getOrderReviews = async () => {
  const response = await api.get('/orders/');
  return response.data;
};

export const getOrderReview = async (id: number) => {
  const response = await api.get(`/orders/${id}/`);
  return response.data;
};

export const createOrderReview = async (data: any) => {
  const response = await api.post('/orders/', data);
  return response.data;
};

export const approveOrderReview = async (id: number) => {
  const response = await api.post(`/orders/${id}/approve/`);
  return response.data;
};

// Confirmed Orders API
export const getConfirmedOrders = async () => {
  const response = await api.get('/confirmed/');
  return response.data;
};

export const createConfirmedOrder = async (data: any) => {
  const response = await api.post('/confirmed/', data);
  return response.data;
};

// Upload CAD file for a specific order
export const uploadCadFile = async (orderId: number, cadFile: File) => {
  const formData = new FormData();
  formData.append('cad_file', cadFile);

  const response = await api.patch(`/confirmed/${orderId}/upload_cad/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

const apiService = {
  getLeads,
  getLead,
  createLead,
  updateLead,
  updateLeadStatus,
  deleteLead,
  getCatalogueItems,
  getCatalogueItem,
  getOrderReviews,
  getOrderReview,
  createOrderReview,
  approveOrderReview,
  getConfirmedOrders,
  createConfirmedOrder,
  uploadCadFile,
};

export default apiService;
