# Unified Order Dashboard – Phase 1

This project is part of the digital transformation initiative for Symetree. In this phase, we are building a Unified Order Dashboard that includes:

1. A Sales Representative Dashboard (Tablet-first experience)
2. A Marketing Dashboard
3. A Production Dashboard

---

## 🔧 Tech Stack

- Backend: Django (Python)
- Frontend: React (TypeScript with TSX)
- Database: PostgreSQL or SQLite (for development)
- API: Django REST Framework

---

## 📁 Project Structure Overview

/backend └── django_app/ ├── models.py ├── serializers.py ├── views.py ├── urls.py /frontend └── src/ ├── components/ ├── pages/ ├── api/ ├── App.tsx


---

## 🧑‍💼 1. Sales Representative Dashboard (Tablet View)

### Purpose:
Sales reps collect lead data through a multi-step form and help the customer either upload a design or select from the in-house catalogue.

### Instructions:

1. Create a React TSX multi-step form with **9 steps**, displaying **one question per screen**.
2. Use a state machine or step-based navigation (e.g. React Router or Zustand).
3. After customer details, in **Step 8**, give two options:
   - Upload a design (image upload or Google Image URL)
   - Browse in-house **catalogue** and select preferred pieces
4. Display catalogue as a grid of image cards with filters by:
   - Budget
   - Occasion
   - Category (ring, necklace, etc.)
5. Save all data and send a POST request to `/api/leads/`
6. Show a success screen after submission.

### Fields Collected by Sales Rep:
- `name`
- `phone`
- `address`
- `budget`
- `occasion` (daily wear, birthday, engagement, wedding, festival, other)
- `timeline` (date)
- `design_reference` (file or URL)
- `catalogue_selection` (list of IDs)
- `remarks` (text)

---

## 📊 2. Marketing Dashboard

### Purpose:
To view and manage leads, segment customers by occasion, budget, and timeline.

### Instructions:

1. Create a page `/marketing` that fetches all leads from `/api/leads/`
2. Display leads in a table with filters:
   - Occasion
   - Budget range
   - Timeline (e.g., urgent, 1–3 months, later)
3. Add segmentation tags to each row based on:
   - Price sensitivity
   - Urgency
   - Occasion type
4. Show lead status: New, Contacted, Warm, Converted, Dropped
5. Add a “Follow-up” button (can be a placeholder for now)

---

## 🏭 3. Production Dashboard

### Purpose:
To review confirmed leads that have become orders, and provide cost and time estimates.

### Instructions:

1. Create a page `/production` that displays only leads with `status = "order"`
2. Each order row shows:
   - Design image or selected catalogue items
   - Occasion, timeline, budget, and remarks
   - Order sheet data (see below)
3. Include a "Review" button to:
   - Approve or reject the order
   - Input estimated cost
   - Input estimated delivery date
   - Add production notes
4. POST this data to `/api/orders/` and update lead accordingly

---

## 📦 Django Models

### Lead Model

```python
class Lead(models.Model):
    name = models.CharField(max_length=100)
    phone = models.CharField(max_length=15)
    address = models.TextField()
    budget = models.IntegerField()
    occasion = models.CharField(max_length=20)
    timeline = models.DateField()
    design_reference = models.URLField(blank=True, null=True)
    design_file = models.FileField(upload_to='designs/', blank=True, null=True)
    remarks = models.TextField(blank=True)
    status = models.CharField(max_length=20, default="lead")  # lead / order
    catalogue_selection = models.ManyToManyField("CatalogueItem", blank=True)


class CatalogueItem(models.Model):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to="catalogue/")
    price = models.IntegerField()
    category = models.CharField(max_length=50)  # e.g. necklace, earring, etc.

    # Optional internal attributes (not shown to sales rep)
    neckline_type = models.CharField(max_length=50, blank=True, null=True)
    post_type = models.CharField(max_length=10, blank=True, null=True)
    post_diameter_mm = models.FloatField(blank=True, null=True)
    omega_gap_mm = models.FloatField(blank=True, null=True)
    bangle_type = models.CharField(max_length=10, blank=True, null=True)
    bangle_lock_type = models.CharField(max_length=10, blank=True, null=True)
    bracelet_lock_type = models.CharField(max_length=10, blank=True, null=True)
    stone_quality = models.CharField(max_length=100, blank=True, null=True)
    stone_color = models.CharField(max_length=100, blank=True, null=True)
    polki_type = models.CharField(max_length=50, blank=True, null=True)
    jadai_type = models.CharField(max_length=20, blank=True, null=True)
    polki_finish = models.CharField(max_length=10, blank=True, null=True)
    diamond_color = models.CharField(max_length=50, blank=True, null=True)
    diamond_quality = models.CharField(max_length=50, blank=True, null=True)
    diamond_certified = models.BooleanField(default=False)
    chilai_engraving = models.BooleanField(default=False)
    laser_solder = models.BooleanField(default=False)
    meena = models.TextField(blank=True, null=True)
    puwai_fall = models.CharField(max_length=100, blank=True, null=True)
    j_patti_side_corners = models.CharField(max_length=100, blank=True, null=True)
    j_patti_thickness = models.CharField(max_length=100, blank=True, null=True)
    foiling = models.BooleanField(default=False)
    foiling_part = models.CharField(max_length=100, blank=True, null=True)
    daak_type = models.CharField(max_length=20, blank=True, null=True)
    daak_quality = models.CharField(max_length=20, blank=True, null=True)
    marking = models.CharField(max_length=20, blank=True, null=True)
    stamping_purity = models.CharField(max_length=100, blank=True, null=True)
    photoshoot_creative = models.BooleanField(default=False)
    photoshoot_video = models.BooleanField(default=False)
    packaging_type = models.CharField(max_length=20, blank=True, null=True)



class ConfirmedOrder(models.Model):
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE)
    po_number = models.CharField(max_length=50)
    customer_name = models.CharField(max_length=100)
    model_number = models.CharField(max_length=50)
    jewel_type = models.CharField(max_length=50)
    stone = models.CharField(max_length=100)
    purity = models.CharField(max_length=10)
    pcs = models.IntegerField()
    size = models.CharField(max_length=20)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    order_date = models.DateField()
    delivery_date = models.DateField()
    remarks = models.TextField(blank=True, null=True)



class OrderReview(models.Model):
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE)
    approved = models.BooleanField()
    estimated_cost = models.IntegerField()
    estimated_delivery = models.DateField()
    notes = models.TextField(blank=True)


📩 API Endpoints
Endpoint	Method	Description
/api/leads/	GET	List all leads
/api/leads/	POST	Create a new lead
/api/leads/<id>/	GET	View single lead
/api/orders/	GET	List all production reviews
/api/orders/	POST	Submit production review
/api/catalogue/	GET	List catalogue items
/api/confirmed/	POST	Add confirmed customer order
/api/confirmed/	GET	View confirmed order list