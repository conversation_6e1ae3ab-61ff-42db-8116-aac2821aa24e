# Symetree Unified Order Dashboard

This project is a digital transformation initiative for Symetree, consisting of a Unified Order Dashboard that includes:

1. A Sales Representative Dashboard (Tablet-first experience)
2. A Marketing Dashboard
3. A Production Dashboard

## Tech Stack

- **Backend**: Django (Python) with Django REST Framework
- **Frontend**: React (TypeScript with TSX)
- **Database**: SQLite (development), can be migrated to PostgreSQL for production
- **UI Components**: Material-UI

## Project Structure

The project is organized as follows:

```
/
├── backend/                  # Django backend
│   ├── django_app/           # Main application
│   │   ├── models.py         # Database models
│   │   ├── serializers.py    # API serializers
│   │   ├── views.py          # API views and logic
│   │   └── urls.py           # API endpoints
│   └── symetree_backend/     # Django project settings
├── frontend/                 # React frontend
│   ├── public/               # Static files
│   └── src/                  # Source code
│       ├── components/       # React components
│       ├── pages/            # Page components
│       ├── api/              # API services
│       ├── store/            # State management (Zustand)
│       └── hooks/            # Custom React hooks
```

## Features

### 1. Sales Representative Dashboard
- Multi-step form for lead collection
- Design reference upload or catalogue selection
- Mobile and tablet-friendly interface

### 2. Marketing Dashboard
- Lead management and filtering
- Customer segmentation
- Follow-up tracking

### 3. Production Dashboard
- Order review workflow
- Cost and timeline estimation
- Production notes

## Getting Started

### Prerequisites
- Node.js and npm
- Python 3.8+
- Pip

### Setup and Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd symetree
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py createsuperuser  # Create an admin user
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

4. **Running the Development Servers**
   
   Backend:
   ```bash
   cd backend
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
   python manage.py runserver
   ```
   
   Frontend:
   ```bash
   cd frontend
   npm start
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - API: http://localhost:8000/api/
   - Admin: http://localhost:8000/admin/

## API Endpoints

- **GET/POST /api/leads/**: List or create leads
- **GET/PUT/DELETE /api/leads/<id>/**: Retrieve, update or delete a specific lead
- **GET/POST /api/catalogue/**: List or create catalogue items
- **GET/POST /api/orders/**: List or create order reviews
- **GET/POST /api/confirmed/**: List or create confirmed orders

## License

This project is proprietary and confidential. 