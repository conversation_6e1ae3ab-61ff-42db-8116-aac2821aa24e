# Generated by Django 5.2 on 2025-05-27 18:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('django_app', '0004_confirmedorder_cad_file_alter_lead_occasion_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='lead',
            name='conversion_probability',
            field=models.IntegerField(default=0, help_text='Conversion probability percentage (0-100)'),
        ),
        migrations.AddField(
            model_name='lead',
            name='previously_purchased',
            field=models.BooleanField(default=False, help_text='Has the customer previously purchased from Symetree?'),
        ),
        migrations.AddField(
            model_name='lead',
            name='retail_potential',
            field=models.CharField(blank=True, choices=[('A', 'A - Excellent'), ('B', 'B - Good'), ('C', 'C - Average'), ('D', 'D - Below Average'), ('E', 'E - Poor')], help_text='Retail potential rating from A to E', max_length=1, null=True),
        ),
        migrations.AddField(
            model_name='lead',
            name='store_representative',
            field=models.CharField(blank=True, help_text='Name of the store representative', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='lead',
            name='timeline',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
