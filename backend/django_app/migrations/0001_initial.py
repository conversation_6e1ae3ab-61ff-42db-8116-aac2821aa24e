# Generated by Django 5.2 on 2025-04-08 12:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CatalogueItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('image', models.ImageField(upload_to='catalogue/')),
                ('price', models.IntegerField()),
                ('category', models.CharField(max_length=50)),
                ('neckline_type', models.CharField(blank=True, max_length=50, null=True)),
                ('post_type', models.CharField(blank=True, max_length=10, null=True)),
                ('post_diameter_mm', models.FloatField(blank=True, null=True)),
                ('omega_gap_mm', models.FloatField(blank=True, null=True)),
                ('bangle_type', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('bangle_lock_type', models.Char<PERSON><PERSON>(blank=True, max_length=10, null=True)),
                ('bracelet_lock_type', models.CharField(blank=True, max_length=10, null=True)),
                ('stone_quality', models.CharField(blank=True, max_length=100, null=True)),
                ('stone_color', models.CharField(blank=True, max_length=100, null=True)),
                ('polki_type', models.CharField(blank=True, max_length=50, null=True)),
                ('jadai_type', models.CharField(blank=True, max_length=20, null=True)),
                ('polki_finish', models.CharField(blank=True, max_length=10, null=True)),
                ('diamond_color', models.CharField(blank=True, max_length=50, null=True)),
                ('diamond_quality', models.CharField(blank=True, max_length=50, null=True)),
                ('diamond_certified', models.BooleanField(default=False)),
                ('chilai_engraving', models.BooleanField(default=False)),
                ('laser_solder', models.BooleanField(default=False)),
                ('meena', models.TextField(blank=True, null=True)),
                ('puwai_fall', models.CharField(blank=True, max_length=100, null=True)),
                ('j_patti_side_corners', models.CharField(blank=True, max_length=100, null=True)),
                ('j_patti_thickness', models.CharField(blank=True, max_length=100, null=True)),
                ('foiling', models.BooleanField(default=False)),
                ('foiling_part', models.CharField(blank=True, max_length=100, null=True)),
                ('daak_type', models.CharField(blank=True, max_length=20, null=True)),
                ('daak_quality', models.CharField(blank=True, max_length=20, null=True)),
                ('marking', models.CharField(blank=True, max_length=20, null=True)),
                ('stamping_purity', models.CharField(blank=True, max_length=100, null=True)),
                ('photoshoot_creative', models.BooleanField(default=False)),
                ('photoshoot_video', models.BooleanField(default=False)),
                ('packaging_type', models.CharField(blank=True, max_length=20, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Lead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('phone', models.CharField(max_length=15)),
                ('address', models.TextField()),
                ('budget', models.IntegerField()),
                ('occasion', models.CharField(choices=[('daily_wear', 'Daily Wear'), ('birthday', 'Birthday'), ('engagement', 'Engagement'), ('wedding', 'Wedding'), ('festival', 'Festival'), ('other', 'Other')], max_length=20)),
                ('timeline', models.DateField()),
                ('design_reference', models.URLField(blank=True, null=True)),
                ('design_file', models.FileField(blank=True, null=True, upload_to='designs/')),
                ('remarks', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('new', 'New'), ('contacted', 'Contacted'), ('warm', 'Warm'), ('converted', 'Converted'), ('dropped', 'Dropped'), ('order', 'Order')], default='new', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('catalogue_selection', models.ManyToManyField(blank=True, to='django_app.catalogueitem')),
            ],
        ),
        migrations.CreateModel(
            name='ConfirmedOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_number', models.CharField(max_length=50)),
                ('customer_name', models.CharField(max_length=100)),
                ('model_number', models.CharField(max_length=50)),
                ('jewel_type', models.CharField(max_length=50)),
                ('stone', models.CharField(max_length=100)),
                ('purity', models.CharField(max_length=10)),
                ('pcs', models.IntegerField()),
                ('size', models.CharField(max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('order_date', models.DateField()),
                ('delivery_date', models.DateField()),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('lead', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='django_app.lead')),
            ],
        ),
        migrations.CreateModel(
            name='OrderReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('approved', models.BooleanField()),
                ('estimated_cost', models.IntegerField()),
                ('estimated_delivery', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('lead', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='django_app.lead')),
            ],
        ),
    ]
