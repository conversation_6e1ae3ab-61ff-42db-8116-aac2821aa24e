from django.contrib import admin
from .models import Lead, CatalogueItem, OrderReview, ConfirmedOrder

@admin.register(CatalogueItem)
class CatalogueItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'price')
    list_filter = ('category',)
    search_fields = ('name', 'category')


@admin.register(Lead)
class LeadAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'budget', 'occasion', 'timeline', 'status')
    list_filter = ('status', 'occasion')
    search_fields = ('name', 'phone')
    date_hierarchy = 'created_at'


@admin.register(OrderReview)
class OrderReviewAdmin(admin.ModelAdmin):
    list_display = ('lead', 'approved', 'estimated_cost', 'estimated_delivery')
    list_filter = ('approved',)
    search_fields = ('lead__name',)
    date_hierarchy = 'created_at'


@admin.register(ConfirmedOrder)
class ConfirmedOrderAdmin(admin.ModelAdmin):
    list_display = ('customer_name', 'po_number', 'jewel_type', 'order_date', 'delivery_date')
    list_filter = ('jewel_type', 'order_date')
    search_fields = ('customer_name', 'po_number')
    date_hierarchy = 'order_date'
