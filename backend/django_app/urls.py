from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import (
    LeadViewSet,
    CatalogueItemViewSet,
    OrderReviewViewSet,
    ConfirmedOrderViewSet,
    login_view,
    user_profile
)

router = DefaultRouter()
router.register(r'leads', LeadViewSet)
router.register(r'catalogue', CatalogueItemViewSet)
router.register(r'orders', OrderReviewViewSet)
router.register(r'confirmed', ConfirmedOrderViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('login/', login_view, name='login'),
    path('profile/', user_profile, name='user-profile'),
] 