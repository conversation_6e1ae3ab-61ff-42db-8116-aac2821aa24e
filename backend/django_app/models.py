from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class UserProfile(models.Model):
    ROLE_CHOICES = (
        ('sales', 'Sales Representative'),
        ('marketing', 'Marketing Department'),
        ('production', 'Production Department'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)

    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"

class CatalogueItem(models.Model):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to="catalogue/")
    price = models.IntegerField()
    category = models.CharField(max_length=50)  # e.g. necklace, earring, etc.

    # Optional internal attributes (not shown to sales rep)
    neckline_type = models.CharField(max_length=50, blank=True, null=True)
    post_type = models.CharField(max_length=10, blank=True, null=True)
    post_diameter_mm = models.FloatField(blank=True, null=True)
    omega_gap_mm = models.FloatField(blank=True, null=True)
    bangle_type = models.CharField(max_length=10, blank=True, null=True)
    bangle_lock_type = models.CharField(max_length=10, blank=True, null=True)
    bracelet_lock_type = models.CharField(max_length=10, blank=True, null=True)
    stone_quality = models.CharField(max_length=100, blank=True, null=True)
    stone_color = models.CharField(max_length=100, blank=True, null=True)
    polki_type = models.CharField(max_length=50, blank=True, null=True)
    jadai_type = models.CharField(max_length=20, blank=True, null=True)
    polki_finish = models.CharField(max_length=10, blank=True, null=True)
    diamond_color = models.CharField(max_length=50, blank=True, null=True)
    diamond_quality = models.CharField(max_length=50, blank=True, null=True)
    diamond_certified = models.BooleanField(default=False)
    chilai_engraving = models.BooleanField(default=False)
    laser_solder = models.BooleanField(default=False)
    meena = models.TextField(blank=True, null=True)
    puwai_fall = models.CharField(max_length=100, blank=True, null=True)
    j_patti_side_corners = models.CharField(max_length=100, blank=True, null=True)
    j_patti_thickness = models.CharField(max_length=100, blank=True, null=True)
    foiling = models.BooleanField(default=False)
    foiling_part = models.CharField(max_length=100, blank=True, null=True)
    daak_type = models.CharField(max_length=20, blank=True, null=True)
    daak_quality = models.CharField(max_length=20, blank=True, null=True)
    marking = models.CharField(max_length=20, blank=True, null=True)
    stamping_purity = models.CharField(max_length=100, blank=True, null=True)
    photoshoot_creative = models.BooleanField(default=False)
    photoshoot_video = models.BooleanField(default=False)
    packaging_type = models.CharField(max_length=20, blank=True, null=True)

    def __str__(self):
        return f"{self.name} - {self.category}"


class Lead(models.Model):
    STATUS_CHOICES = (
        ('new', 'Just In'),
        ('contacted', 'Contacted'),
        ('warm', 'Warm'),
        ('converted', 'Converted'),
        ('dropped', 'Dropped'),
        ('order', 'Order'),
    )

    OCCASION_CHOICES = (
        ('wedding', 'Wedding'),
        ('special_occasions', 'Special occasions'),
        ('casual', 'Casual'),
        ('other', 'Other'),
    )

    JEWELRY_CATEGORY_CHOICES = (
        ('necklace', 'Necklace'),
        ('bangles', 'Bangles'),
        ('earrings', 'Earrings'),
        ('rings', 'Rings'),
        ('bracelets', 'Bracelets'),
        ('pendants', 'Pendants'),
        ('chains', 'Chains'),
        ('anklets', 'Anklets'),
        ('nose_pins', 'Nose Pins'),
        ('toe_rings', 'Toe Rings'),
        ('maang_tikka', 'Maang Tikka'),
        ('sets', 'Jewelry Sets'),
        ('other', 'Other'),
    )

    RETAIL_POTENTIAL_CHOICES = (
        ('A', 'A - Excellent'),
        ('B', 'B - Good'),
        ('C', 'C - Average'),
        ('D', 'D - Below Average'),
        ('E', 'E - Poor'),
    )

    name = models.CharField(max_length=100)
    phone = models.CharField(max_length=15)
    address = models.TextField(blank=True, null=True)
    budget = models.IntegerField()
    occasion = models.CharField(max_length=20, choices=OCCASION_CHOICES)
    timeline = models.CharField(max_length=100, blank=True, null=True)  # Changed to CharField for flexibility
    design_reference = models.URLField(blank=True, null=True)
    design_file = models.FileField(upload_to='designs/', blank=True, null=True)
    remarks = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="new")
    catalogue_selection = models.ManyToManyField(CatalogueItem, blank=True)
    jewelry_categories = models.CharField(max_length=255, blank=True, help_text="Comma-separated list of jewelry categories")
    store = models.CharField(max_length=50, blank=True, null=True)

    # New fields
    previously_purchased = models.BooleanField(default=False, help_text="Has the customer previously purchased from Symetree?")
    conversion_probability = models.IntegerField(default=0, help_text="Conversion probability percentage (0-100)")
    retail_potential = models.CharField(max_length=1, choices=RETAIL_POTENTIAL_CHOICES, blank=True, null=True, help_text="Retail potential rating from A to E")
    store_representative = models.CharField(max_length=100, blank=True, null=True, help_text="Name of the store representative")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.occasion} - {self.status}"


class OrderReview(models.Model):
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE)
    approved = models.BooleanField()
    estimated_cost = models.IntegerField()
    estimated_delivery = models.DateField()
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Review for {self.lead.name} - {'Approved' if self.approved else 'Rejected'}"


class ConfirmedOrder(models.Model):
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE)
    po_number = models.CharField(max_length=50)
    customer_name = models.CharField(max_length=100)
    model_number = models.CharField(max_length=50)
    jewel_type = models.CharField(max_length=50)
    stone = models.CharField(max_length=100)
    purity = models.CharField(max_length=10)
    pcs = models.IntegerField()
    size = models.CharField(max_length=20)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    order_date = models.DateField()
    delivery_date = models.DateField()
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    cad_file = models.FileField(upload_to='cad_files/', blank=True, null=True)

    def __str__(self):
        return f"{self.customer_name} - {self.po_number}"
