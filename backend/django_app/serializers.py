from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Lead, CatalogueItem, OrderReview, ConfirmedOrder, UserProfile

class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['role']

class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'profile']
        read_only_fields = ['id']

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(write_only=True, style={'input_type': 'password'})
    role = serializers.CharField(read_only=True)

class CatalogueItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = CatalogueItem
        fields = '__all__'


class LeadSerializer(serializers.ModelSerializer):
    catalogue_selection = CatalogueItemSerializer(many=True, read_only=True)
    catalogue_ids = serializers.PrimaryKeyRelatedField(
        queryset=CatalogueItem.objects.all(),
        write_only=True,
        many=True,
        required=False,
        source='catalogue_selection'
    )

    class Meta:
        model = Lead
        fields = [
            'id', 'name', 'phone', 'address', 'budget', 'occasion',
            'timeline', 'design_reference', 'design_file', 'remarks',
            'status', 'catalogue_selection', 'catalogue_ids',
            'jewelry_categories', 'store', 'previously_purchased',
            'conversion_probability', 'retail_potential', 'store_representative',
            'created_at', 'updated_at'
        ]

    def validate_budget(self, value):
        """Convert budget string to integer if needed"""
        if isinstance(value, str):
            try:
                return int(value)
            except ValueError:
                raise serializers.ValidationError("Budget must be a valid number")
        return value


class OrderReviewSerializer(serializers.ModelSerializer):
    lead_details = LeadSerializer(source='lead', read_only=True)

    class Meta:
        model = OrderReview
        fields = [
            'id', 'lead', 'lead_details', 'approved', 'estimated_cost',
            'estimated_delivery', 'notes', 'created_at'
        ]
        read_only_fields = ['created_at']


class ConfirmedOrderSerializer(serializers.ModelSerializer):
    lead_details = LeadSerializer(source='lead', read_only=True)
    cad_file = serializers.FileField(required=False, allow_null=True)

    class Meta:
        model = ConfirmedOrder
        fields = [
            'id', 'lead', 'lead_details', 'po_number', 'customer_name',
            'model_number', 'jewel_type', 'stone', 'purity', 'pcs',
            'size', 'amount', 'order_date', 'delivery_date', 'remarks',
            'created_at', 'cad_file'
        ]
        read_only_fields = ['created_at']