import requests
import json
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class WhatsAppService:
    """Service for sending WhatsApp messages via Interakt.ai API"""

    def __init__(self):
        self.api_url = "https://api.interakt.ai/v1/public/message/"
        self.auth_token = getattr(settings, 'INTERAKT_AUTH_TOKEN', 'QkxDeW5TZ2RPU2hSdlMzVHZyMi0xSVZ4SmtxbGtyb0V0Vkwtd3h1NzlFNDo=')
        self.recipient_numbers = [
            "7732847694",
            "9001013571",
            "9829068777"
        ]

    def format_store_name(self, store_code):
        """Convert store code to readable name"""
        store_mapping = {
            'khan_market': 'Khan Market',
            'amrawatta': 'Amrawatta'
        }
        return store_mapping.get(store_code, store_code.replace('_', ' ').title())

    def format_jewelry_type(self, jewelry_categories):
        """Extract and format jewelry type from categories"""
        if not jewelry_categories:
            return "Jewelry"

        # Mapping for better display names
        category_mapping = {
            'necklace': 'Necklace',
            'bangles': 'Bangles',
            'earrings': 'Earrings',
            'rings': 'Rings',
            'bracelets': 'Bracelets',
            'pendants': 'Pendants',
            'chains': 'Chains',
            'anklets': 'Anklets',
            'nose_pins': 'Nose Pins',
            'toe_rings': 'Toe Rings',
            'maang_tikka': 'Maang Tikka',
            'sets': 'Jewelry Sets',
            'other': 'Other'
        }

        # Split by comma and take the first category
        categories = jewelry_categories.split(',')
        if categories:
            category = categories[0].strip().lower()
            return category_mapping.get(category, category.replace('_', ' ').title())
        return "Jewelry"

    def format_occasion(self, occasion):
        """Convert occasion code to readable format"""
        occasion_mapping = {
            'wedding': 'Wedding',
            'special_occasions': 'Special Occasions',
            'casual': 'Casual',
            'other': 'Other'
        }
        return occasion_mapping.get(occasion, occasion.replace('_', ' ').title())

    def format_price(self, budget):
        """Format budget as currency"""
        try:
            # Convert to integer if it's a string
            if isinstance(budget, str):
                budget = float(budget)
            return f"₹{int(budget):,}"
        except (ValueError, TypeError):
            return f"₹{budget}"

    def send_lead_notification(self, lead_data):
        """
        Send WhatsApp notification for new lead

        Args:
            lead_data: Dictionary containing lead information
                - store: Store location code
                - name: Customer name
                - jewelry_categories: Type of jewelry
                - budget: Quoted price
                - occasion: Occasion for the jewelry
        """
        try:
            # Format the data for the template
            store_name = self.format_store_name(lead_data.get('store', ''))
            customer_name = lead_data.get('name', 'Unknown')
            jewelry_type = self.format_jewelry_type(lead_data.get('jewelry_categories', ''))
            quoted_price = self.format_price(lead_data.get('budget', 0))
            occasion = self.format_occasion(lead_data.get('occasion', ''))

            # Template body values in the required order
            body_values = [
                store_name,
                customer_name,
                jewelry_type,
                quoted_price,
                occasion
            ]

            # Send message to each recipient
            success_count = 0
            for phone_number in self.recipient_numbers:
                try:
                    success = self._send_message(phone_number, body_values)
                    if success:
                        success_count += 1
                except Exception as e:
                    logger.error(f"Failed to send WhatsApp message to {phone_number}: {str(e)}")
                    continue

            logger.info(f"WhatsApp notifications sent successfully to {success_count}/{len(self.recipient_numbers)} recipients")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error in send_lead_notification: {str(e)}")
            return False

    def _send_message(self, phone_number, body_values):
        """
        Send WhatsApp message to a single recipient

        Args:
            phone_number: Recipient phone number (without country code)
            body_values: List of values for the template

        Returns:
            bool: True if message sent successfully, False otherwise
        """
        try:
            headers = {
                'Authorization': f'Basic {self.auth_token}',
                'Content-Type': 'application/json'
            }

            payload = {
                "countryCode": "+91",
                "phoneNumber": phone_number,
                "type": "Template",
                "template": {
                    "name": "lead_new",
                    "languageCode": "en",
                    "bodyValues": body_values
                }
            }

            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"WhatsApp message sent successfully to +91{phone_number}")
                return True
            else:
                logger.error(f"Failed to send WhatsApp message to +91{phone_number}. Status: {response.status_code}, Response: {response.text}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error sending WhatsApp message to +91{phone_number}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending WhatsApp message to +91{phone_number}: {str(e)}")
            return False


# Convenience function for easy import
def send_lead_whatsapp_notification(lead_data):
    """
    Convenience function to send WhatsApp notification for new lead

    Args:
        lead_data: Dictionary or model instance containing lead information
    """
    service = WhatsAppService()

    # Convert model instance to dictionary if needed
    if hasattr(lead_data, '__dict__'):
        lead_dict = {
            'store': getattr(lead_data, 'store', ''),
            'name': getattr(lead_data, 'name', ''),
            'jewelry_categories': getattr(lead_data, 'jewelry_categories', ''),
            'budget': getattr(lead_data, 'budget', 0),
            'occasion': getattr(lead_data, 'occasion', '')
        }
    else:
        lead_dict = lead_data

    return service.send_lead_notification(lead_dict)
