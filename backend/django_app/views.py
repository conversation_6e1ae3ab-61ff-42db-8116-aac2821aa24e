from django.shortcuts import render
from rest_framework import viewsets, status, permissions
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate
from .models import Lead, CatalogueItem, OrderReview, ConfirmedOrder, UserProfile
from .serializers import (
    LeadSerializer,
    CatalogueItemSerializer,
    OrderReviewSerializer,
    ConfirmedOrderSerializer,
    UserSerializer,
    LoginSerializer
)
from rest_framework.parsers import MultiPartParser, FormParser
from .services.whatsapp_service import send_lead_whatsapp_notification
import logging

logger = logging.getLogger(__name__)

# Create your views here.

class IsMarketingUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'profile') and request.user.profile.role == 'marketing'

class IsSalesUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'profile') and request.user.profile.role == 'sales'

class IsProductionUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'profile') and request.user.profile.role == 'production'

@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        user = authenticate(username=username, password=password)

        if user:
            token, created = Token.objects.get_or_create(user=user)

            # Get user role
            try:
                role = user.profile.role
            except UserProfile.DoesNotExist:
                return Response(
                    {"error": "User profile not found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({
                "token": token.key,
                "user_id": user.id,
                "username": user.username,
                "role": role
            })
        else:
            return Response(
                {"error": "Invalid credentials"},
                status=status.HTTP_401_UNAUTHORIZED
            )
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    serializer = UserSerializer(request.user)
    return Response(serializer.data)

class LeadViewSet(viewsets.ModelViewSet):
    queryset = Lead.objects.all()
    serializer_class = LeadSerializer

    def get_permissions(self):
        if self.action == 'list' or self.action == 'retrieve':
            permission_classes = [IsAuthenticated]
        elif self.action == 'create' or self.action == 'update' or self.action == 'partial_update':
            permission_classes = [IsAuthenticated, IsSalesUser|IsMarketingUser]
        else:
            permission_classes = [IsAuthenticated, IsMarketingUser]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = Lead.objects.all()

        # Filter by status if provided
        status = self.request.query_params.get('status', None)
        if status:
            queryset = queryset.filter(status=status)

        # Filter by occasion if provided
        occasion = self.request.query_params.get('occasion', None)
        if occasion:
            queryset = queryset.filter(occasion=occasion)

        # Filter by store if provided
        store = self.request.query_params.get('store', None)
        if store:
            queryset = queryset.filter(store=store)

        # Filter by jewelry categories if provided
        jewelry_categories = self.request.query_params.get('jewelry_categories', None)
        if jewelry_categories:
            queryset = queryset.filter(jewelry_categories__icontains=jewelry_categories)

        # Filter by budget range if provided
        min_budget = self.request.query_params.get('min_budget', None)
        max_budget = self.request.query_params.get('max_budget', None)

        if min_budget:
            queryset = queryset.filter(budget__gte=min_budget)
        if max_budget:
            queryset = queryset.filter(budget__lte=max_budget)

        return queryset

    def create(self, request, *args, **kwargs):
        """Override create method to send WhatsApp notification after lead creation"""
        # Debug: Print request data
        print(f"DEBUG: Request data: {request.data}")

        # Create serializer and validate
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print(f"DEBUG: Serializer errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Save the lead
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        response = Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

        if response.status_code == status.HTTP_201_CREATED:
            # Get the created lead instance
            lead_id = response.data.get('id')
            try:
                lead = Lead.objects.get(id=lead_id)
                # Send WhatsApp notification asynchronously (non-blocking)
                try:
                    send_lead_whatsapp_notification(lead)
                    logger.info(f"WhatsApp notification sent for lead {lead_id}")
                except Exception as e:
                    # Log the error but don't fail the lead creation
                    logger.error(f"Failed to send WhatsApp notification for lead {lead_id}: {str(e)}")
            except Lead.DoesNotExist:
                logger.error(f"Lead {lead_id} not found after creation")

        return response

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        lead = self.get_object()
        status = request.data.get('status')

        if status:
            lead.status = status
            lead.save()
            return Response({'status': 'status updated'})
        return Response({'error': 'No status provided'}, status=400)


class CatalogueItemViewSet(viewsets.ModelViewSet):
    queryset = CatalogueItem.objects.all()
    serializer_class = CatalogueItemSerializer

    def get_permissions(self):
        if self.action == 'list' or self.action == 'retrieve':
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAuthenticated, IsSalesUser]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = CatalogueItem.objects.all()

        # Filter by category if provided
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category=category)

        # Filter by price range
        min_price = self.request.query_params.get('min_price', None)
        max_price = self.request.query_params.get('max_price', None)

        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        return queryset


class OrderReviewViewSet(viewsets.ModelViewSet):
    queryset = OrderReview.objects.all()
    serializer_class = OrderReviewSerializer

    def get_permissions(self):
        if self.action == 'list' or self.action == 'retrieve':
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAuthenticated, IsProductionUser]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        review = self.get_object()
        review.approved = True
        review.save()

        # Update the lead status to 'order'
        lead = review.lead
        lead.status = 'order'
        lead.save()

        return Response({'status': 'order approved'})


class ConfirmedOrderViewSet(viewsets.ModelViewSet):
    queryset = ConfirmedOrder.objects.all()
    serializer_class = ConfirmedOrderSerializer
    parser_classes = (MultiPartParser, FormParser)

    def get_permissions(self):
        if self.action == 'list' or self.action == 'retrieve':
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAuthenticated, IsProductionUser]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['patch'], url_path='upload_cad', parser_classes=[MultiPartParser, FormParser])
    def upload_cad(self, request, pk=None):
        """
        Custom endpoint to upload or update the CAD file for a specific order.
        """
        order = self.get_object()
        serializer = self.get_serializer(order, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({'status': 'CAD file uploaded', 'cad_file': serializer.data.get('cad_file')})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
