LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.

#!/usr/bin/env python
"""
Test script for WhatsApp service
Run this script to test the WhatsApp notification functionality
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'symetree_backend.settings')
django.setup()

from django_app.services.whatsapp_service import send_lead_whatsapp_notification

def test_whatsapp_notification():
    """Test the WhatsApp notification with sample data"""
    
    # Sample lead data
    sample_lead_data = {
        'store': 'khan_market',
        'name': 'Prashant Pandey',
        'jewelry_categories': 'ring,necklace',
        'budget': 50000,
        'occasion': 'wedding'
    }
    
    print("Testing WhatsApp notification...")
    print(f"Sample data: {sample_lead_data}")
    
    try:
        result = send_lead_whatsapp_notification(sample_lead_data)
        if result:
            print("✅ WhatsApp notification sent successfully!")
        else:
            print("❌ WhatsApp notification failed to send")
    except Exception as e:
        print(f"❌ Error sending WhatsApp notification: {str(e)}")

if __name__ == "__main__":
    test_whatsapp_notification()
