import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'symetree_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django_app.models import UserProfile

# Create test users
def create_user(username, password, email, first_name, last_name, role):
    # Check if user already exists
    if User.objects.filter(username=username).exists():
        user = User.objects.get(username=username)
        print(f"User {username} already exists")
    else:
        # Create user
        user = User.objects.create_user(
            username=username,
            password=password,
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        print(f"Created user: {username}")
    
    # Create or update profile
    try:
        profile = UserProfile.objects.get(user=user)
        profile.role = role
        profile.save()
        print(f"Updated profile for {username} with role {role}")
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(
            user=user,
            role=role
        )
        print(f"Created profile for {username} with role {role}")
    
    return user

if __name__ == "__main__":
    # Sales user
    create_user(
        username="sales",
        password="sales123",
        email="<EMAIL>",
        first_name="Sales",
        last_name="Rep",
        role="sales"
    )
    
    # Marketing user
    create_user(
        username="marketing",
        password="marketing123",
        email="<EMAIL>",
        first_name="Marketing",
        last_name="Manager",
        role="marketing"
    )
    
    # Production user
    create_user(
        username="production",
        password="production123",
        email="<EMAIL>",
        first_name="Production",
        last_name="Manager",
        role="production"
    )
    
    print("Test users created successfully!") 