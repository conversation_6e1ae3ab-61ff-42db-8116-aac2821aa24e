import React, { ReactElement, useEffect } from 'react';
import { Box, styled } from '@mui/material';

// Update interface to include StepProps-like properties
interface FormWrapperProps {
  children: React.ReactElement<any>;
}

const HiddenButtonsContainer = styled(Box)`
  .MuiBox-root[style*="display: flex"][style*="justify-content"] {
    display: none !important;
  }
`;

/**
 * FormWrapper component that hides the navigation buttons in form components
 * by intercepting and modifying the rendered output
 */
const FormWrapper: React.FC<FormWrapperProps> = ({ children }) => {
  // Clone the child element and modify its props
  // The type casting ensures TypeScript knows we can add these props
  const modifiedChild = React.cloneElement(children, {
    // Override the onNext and onPrev props with empty functions
    onNext: () => {},
    onPrev: () => {},
  } as any); // Use type assertion to bypass TypeScript checking

  // Add global style to hide button containers
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .MuiBox-root[style*="display: flex"][style*="justify-content"] button {
        display: none !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return <HiddenButtonsContainer>{modifiedChild}</HiddenButtonsContainer>;
};

export default FormWrapper;
