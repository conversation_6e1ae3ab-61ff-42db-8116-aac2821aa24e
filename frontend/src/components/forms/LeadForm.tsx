import React, { useState, useRef, useEffect } from 'react';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Box,
  Typography,
  Divider,
  FormControlLabel,
  Tab,
  Tabs,
  Checkbox,
  FormGroup,
  Card,
  CardMedia,
  CardContent,
  CardActionArea,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import ImageIcon from '@mui/icons-material/Image';
import LinkIcon from '@mui/icons-material/Link';
import useLeadFormStore from '../../store/leadFormStore';
import { getCatalogueItems } from '../../api/api';

// Styled components
const StyledFormHeading = styled(Typography)(({ theme }) => ({
  fontFamily: '"Playfair Display", serif',
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.dark,
  fontWeight: 500,
  fontSize: '1.75rem',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 4,
  padding: '12px 28px',
  fontSize: '1rem',
  '&.MuiButton-contained': {
    boxShadow: 'none',
    background: `linear-gradient(45deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
    '&:hover': {
      boxShadow: '0 4px 8px rgba(201, 176, 55, 0.25)',
      background: `linear-gradient(45deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
    },
  },
  '&.MuiButton-outlined': {
    borderColor: theme.palette.primary.main,
    color: theme.palette.primary.main,
    '&:hover': {
      borderColor: theme.palette.primary.dark,
      background: 'rgba(201, 176, 55, 0.05)',
    },
  },
}));

const FormContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

const ButtonContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(4),
  display: 'flex',
  justifyContent: 'space-between',
}));

interface StepProps {
  onNext: () => void;
  onPrev?: () => void;
}

// Step 1: Customer Name
export const NameForm: React.FC<StepProps> = ({ onNext }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ name: e.target.value });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Customer Information
      </StyledFormHeading>
      <TextField
        label="Customer Name"
        value={formData.name}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
        variant="outlined"
        InputLabelProps={{
          style: { fontSize: '1.1rem' }
        }}
        inputProps={{
          style: { fontSize: '1.1rem' }
        }}
      />
      <ButtonContainer sx={{ justifyContent: 'flex-end' }}>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={!formData.name}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 2: Phone
export const PhoneForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ phone: e.target.value });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Contact Information
      </StyledFormHeading>
      <TextField
        label="Phone Number"
        value={formData.phone}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
        variant="outlined"
      />
      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={!formData.phone}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 3: Address
export const AddressForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ address: e.target.value });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Address Information
      </StyledFormHeading>
      <TextField
        label="Address"
        value={formData.address}
        onChange={handleChange}
        fullWidth
        required
        multiline
        rows={4}
        margin="normal"
        variant="outlined"
      />
      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={!formData.address}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 4: Budget
export const BudgetForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const budget = e.target.value === '' ? '' : Number(e.target.value);
    updateFormData({ budget });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Budget Information
      </StyledFormHeading>
      <TextField
        label="Budget (₹)"
        type="number"
        value={formData.budget}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
        inputProps={{ min: 0 }}
        variant="outlined"
      />
      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={formData.budget === ''}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 5: Occasion
export const OccasionForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    updateFormData({ occasion: e.target.value as string });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Occasion Information
      </StyledFormHeading>
      <FormControl fullWidth margin="normal" required>
        <InputLabel id="occasion-label" sx={{ fontSize: '1.1rem' }}>Occasion</InputLabel>
        <Select
          labelId="occasion-label"
          value={formData.occasion}
          onChange={handleChange as any}
          label="Occasion"
          variant="outlined"
          sx={{ fontSize: '1.1rem' }}
        >
          <MenuItem value="wedding" sx={{ fontSize: '1.1rem' }}>Wedding</MenuItem>
          <MenuItem value="special_occasions" sx={{ fontSize: '1.1rem' }}>Special occasions</MenuItem>
          <MenuItem value="casual" sx={{ fontSize: '1.1rem' }}>Casual</MenuItem>
          <MenuItem value="other" sx={{ fontSize: '1.1rem' }}>Other</MenuItem>
        </Select>
      </FormControl>
      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={!formData.occasion}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 6: Timeline
export const TimelineForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleDateChange = (date: unknown) => {
    if (date) {
      updateFormData({ timeline: (date as Date).toISOString().split('T')[0] });
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <FormContainer>
        <StyledFormHeading variant="h6">
          Timeline Information
        </StyledFormHeading>
        <DatePicker
          label="Timeline"
          value={formData.timeline ? new Date(formData.timeline) : null}
          onChange={handleDateChange}
          renderInput={(params) => <TextField {...params} fullWidth margin="normal" variant="outlined" />}
        />
        <ButtonContainer>
          <StyledButton
            variant="outlined"
            onClick={onPrev}
          >
            Back
          </StyledButton>
          <StyledButton
            variant="contained"
            color="primary"
            onClick={onNext}
            disabled={!formData.timeline}
          >
            Next
          </StyledButton>
        </ButtonContainer>
      </FormContainer>
    </LocalizationProvider>
  );
};

// Step 7: Remarks
export const RemarksForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ remarks: e.target.value });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Additional Information
      </StyledFormHeading>
      <TextField
        label="Remarks"
        value={formData.remarks}
        onChange={handleChange}
        fullWidth
        multiline
        rows={4}
        margin="normal"
        variant="outlined"
      />
      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 8: Design Reference
export const DesignReferenceForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();
  const [tabValue, setTabValue] = useState(0);
  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ design_reference: e.target.value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      updateFormData({ design_file: file, design_reference: '' });
      setSelectedFileName(file.name);
    }
  };

  const handleCaptureBtnClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.setAttribute('accept', 'image/*');
      fileInputRef.current.click();
    }
  };

  const handleBrowseBtnClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.removeAttribute('capture');
      fileInputRef.current.setAttribute('accept', 'image/*');
      fileInputRef.current.click();
    }
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Design Reference
      </StyledFormHeading>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="design reference tabs">
          <Tab icon={<LinkIcon />} label="Paste URL" />
          <Tab icon={<ImageIcon />} label="Upload Image" />
          <Tab icon={<PhotoCameraIcon />} label="Take Photo" />
        </Tabs>
      </Box>

      {/* URL Input */}
      {tabValue === 0 && (
        <TextField
          label="Image URL"
          value={formData.design_reference}
          onChange={handleUrlChange}
          fullWidth
          margin="normal"
          placeholder="https://example.com/image.jpg"
          variant="outlined"
        />
      )}

      {/* Upload and Camera Options */}
      {(tabValue === 1 || tabValue === 2) && (
        <>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleFileChange}
          />

          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, my: 2 }}>
            {tabValue === 1 ? (
              <Button
                variant="outlined"
                startIcon={<ImageIcon />}
                onClick={handleBrowseBtnClick}
                fullWidth
              >
                Browse for Image
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<PhotoCameraIcon />}
                onClick={handleCaptureBtnClick}
                fullWidth
              >
                Take Photo
              </Button>
            )}

            {selectedFileName && (
              <Typography variant="body2" color="textSecondary">
                Selected: {selectedFileName}
              </Typography>
            )}
          </Box>
        </>
      )}

      <Divider sx={{ my: 3 }} />

      <Typography variant="body2" sx={{ mb: 2 }}>
        Or you can select from our catalogue (to be implemented)
      </Typography>

      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 9: Jewelry Category Selection
export const JewelryCategoryForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const categories = [
    { value: 'necklace', label: 'Necklace' },
    { value: 'earrings', label: 'Earrings' },
    { value: 'bangle', label: 'Bangle' },
    { value: 'bracelet', label: 'Bracelet' },
    { value: 'brooch', label: 'Brooch' },
    { value: 'bridal_collection', label: 'Bridal Collection' }
  ];

  const handleCategoryChange = (category: string) => {
    const updatedCategories = [...formData.jewelry_categories];

    if (updatedCategories.includes(category)) {
      // Remove it if already selected
      const index = updatedCategories.indexOf(category);
      updatedCategories.splice(index, 1);
    } else {
      // Add it if not selected
      updatedCategories.push(category);
    }

    updateFormData({ jewelry_categories: updatedCategories });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Jewelry Categories
      </StyledFormHeading>
      <FormTypography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Select the types of jewelry the customer is interested in:
      </FormTypography>

      <FormGroup>
        {categories.map((category) => (
          <FormControlLabel
            key={category.value}
            control={
              <Checkbox
                checked={formData.jewelry_categories.includes(category.value)}
                onChange={() => handleCategoryChange(category.value)}
                sx={{
                  color: 'primary.main',
                  '&.Mui-checked': {
                    color: 'primary.main',
                  },
                }}
              />
            }
            label={category.label}
          />
        ))}
      </FormGroup>

      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={formData.jewelry_categories.length === 0}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 10: Catalogue Selection
export const CatalogueSelectionForm: React.FC<StepProps> = ({ onNext, onPrev }) => {
  const { formData, updateFormData } = useLeadFormStore();
  const [catalogueItems, setCatalogueItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<number[]>(formData.catalogue_ids || []);
  const [error, setError] = useState<string | null>(null);
  const [shouldSkip, setShouldSkip] = useState(false);

  // Handle skip logic - move conditional check inside useEffect
  useEffect(() => {
    if (formData.design_reference || formData.design_file) {
      setShouldSkip(true);
      onNext();
    } else {
      fetchCatalogueItems();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.design_reference, formData.design_file, onNext]);

  const fetchCatalogueItems = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare filter parameters
      const params: Record<string, string> = {};

      // Filter by budget (use 25% higher than the budget to give some flexibility)
      if (formData.budget) {
        const maxBudget = typeof formData.budget === 'number'
          ? Math.round(formData.budget * 1.25)
          : Math.round(Number(formData.budget) * 1.25);
        params.max_price = maxBudget.toString();
      }

      // Filter by jewelry categories if available
      if (formData.jewelry_categories && formData.jewelry_categories.length > 0) {
        // Map our categories to catalogue categories
        const categoryMap: Record<string, string> = {
          'necklace': 'necklace',
          'earrings': 'earring',
          'bangle': 'bangle',
          'bracelet': 'bracelet',
          'brooch': 'brooch',
          'bridal_collection': 'bridal'
        };

        // Get the first category as filter (we can enhance this later to support multiple)
        const category = formData.jewelry_categories[0];
        if (category && categoryMap[category]) {
          params.category = categoryMap[category];
        }
      }

      const response = await getCatalogueItems(params);
      setCatalogueItems(response.results || []);
    } catch (err) {
      console.error('Error fetching catalogue items:', err);
      setError('Failed to load catalogue items. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleItemSelect = (itemId: number) => {
    setSelectedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  const handleSubmit = () => {
    updateFormData({ catalogue_ids: selectedItems });
    onNext();
  };

  // If we should skip, don't render anything
  if (shouldSkip) {
    return null;
  }

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Select from our Catalogue
      </StyledFormHeading>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress color="primary" />
        </Box>
      ) : error ? (
        <Typography color="error" sx={{ my: 2 }}>
          {error}
        </Typography>
      ) : catalogueItems.length === 0 ? (
        <Typography sx={{ my: 2 }}>
          No matching catalogue items found for your criteria. Try adjusting your budget or jewelry type.
        </Typography>
      ) : (
        <>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Select pieces from our catalogue that match your preferences.
            {formData.budget && (
              <Box component="span" fontWeight="medium">
                {' '}Showing items within ₹{Math.round(Number(formData.budget) * 1.25).toLocaleString()} budget range.
              </Box>
            )}
          </Typography>

          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            margin: theme => theme.spacing(-1),
            mb: 3
          }}>
            {catalogueItems.map(item => (
              <Box
                key={item.id}
                sx={{
                  width: { xs: '50%', sm: '33.33%', md: '25%' },
                  padding: 1
                }}
              >
                <Card
                  elevation={selectedItems.includes(item.id) ? 4 : 1}
                  sx={{
                    border: selectedItems.includes(item.id)
                      ? '2px solid #c9b037'
                      : '1px solid #eee',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <CardActionArea
                    onClick={() => handleItemSelect(item.id)}
                    sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
                  >
                    <CardMedia
                      component="img"
                      height="140"
                      image={item.image}
                      alt={item.name}
                      sx={{
                        objectFit: 'cover',
                        backgroundColor: '#f9f9f9',
                      }}
                    />
                    <CardContent sx={{ flexGrow: 1, pt: 1, pb: 1 }}>
                      <Typography variant="body2" fontWeight={500} noWrap>
                        {item.name}
                      </Typography>
                      <Typography variant="caption" color="primary.dark" fontWeight={600}>
                        ₹{item.price.toLocaleString()}
                      </Typography>
                      <Box display="flex" alignItems="center" mt={0.5}>
                        <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                          {item.category}
                        </Typography>
                        {selectedItems.includes(item.id) && (
                          <Box
                            sx={{
                              ml: 'auto',
                              width: 16,
                              height: 16,
                              borderRadius: '50%',
                              bgcolor: 'primary.main',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Box
                              component="span"
                              sx={{
                                color: 'white',
                                fontSize: '0.6rem',
                                lineHeight: 1,
                                fontWeight: 'bold'
                              }}
                            >
                              ✓
                            </Box>
                          </Box>
                        )}
                      </Box>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Box>
            ))}
          </Box>
        </>
      )}

      <ButtonContainer>
        <StyledButton
          variant="outlined"
          onClick={onPrev}
        >
          Back
        </StyledButton>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={loading}
        >
          {selectedItems.length > 0 ? `Continue with ${selectedItems.length} selected` : 'Skip catalogue selection'}
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 1: Store Selection
export const StoreForm: React.FC<StepProps> = ({ onNext }) => {
  const { formData, updateFormData } = useLeadFormStore();

  const handleChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    updateFormData({ store: e.target.value as string });
  };

  return (
    <FormContainer>
      <StyledFormHeading variant="h6">
        Store Location
      </StyledFormHeading>
      <FormControl fullWidth margin="normal" required>
        <InputLabel id="store-label">Select Store</InputLabel>
        <Select
          labelId="store-label"
          value={formData.store}
          onChange={handleChange as any}
          label="Select Store"
          variant="outlined"
        >
          <MenuItem value="khan_market">Khan Market</MenuItem>
          <MenuItem value="amrawatta">Amrawatta</MenuItem>
        </Select>
      </FormControl>
      <ButtonContainer sx={{ justifyContent: 'flex-end' }}>
        <StyledButton
          variant="contained"
          color="primary"
          onClick={onNext}
          disabled={!formData.store}
        >
          Next
        </StyledButton>
      </ButtonContainer>
    </FormContainer>
  );
};

// Step 10: Success Screen
export const SuccessScreen: React.FC = () => {
  return (
    <FormContainer sx={{ textAlign: 'center', py: 4 }}>
      <Box sx={{
        mb: 3,
        position: 'relative',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: -10,
          left: '50%',
          transform: 'translateX(-50%)',
          width: 100,
          height: 2,
          background: (theme) => `linear-gradient(90deg, transparent, ${theme.palette.primary.main}, transparent)`,
        }
      }}>
        <Typography variant="h5" color="primary.dark" fontFamily="Playfair Display" fontWeight={600} gutterBottom>
          Thank You!
        </Typography>
        <Typography variant="h6" color="primary" fontFamily="Cormorant Garamond" fontWeight={500}>
          Order Submitted Successfully
        </Typography>
      </Box>
      <Typography variant="body1" sx={{ mb: 3 }}>
        The customer's information has been recorded. Our team will craft a beautiful piece of jewelry based on these requirements.
      </Typography>
      <Typography variant="body2" color="text.secondary" fontStyle="italic">
        "Jewelry reflects the beauty within us all." - Symetree
      </Typography>
    </FormContainer>
  );
};

// Add a new styled component for form labels and text
const FormTypography = styled(Typography)(({ theme }) => ({
  fontSize: '1.1rem',
  fontWeight: 400,
  lineHeight: 1.5
}));