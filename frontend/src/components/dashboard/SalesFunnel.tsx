import React from 'react';
import { Box, Paper, Typography, Tooltip, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';

interface FunnelData {
  status: string;
  count: number;
  color: string;
  percentage?: number;
}

interface SalesFunnelProps {
  data: FunnelData[];
  totalLeads: number;
}

const FunnelContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(4),
  borderRadius: 8,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: 'rgba(255, 255, 255, 0.97)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '30%',
    height: '100%',
    background: `linear-gradient(to bottom right, ${alpha(theme.palette.primary.light, 0.1)}, transparent)`,
    zIndex: 0,
  }
}));

const FunnelTitle = styled(Typography)(({ theme }) => ({
  fontFamily: '"Playfair Display", serif',
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.dark,
  fontWeight: 600,
  position: 'relative',
}));

const StageContainer = styled(Box)({
  position: 'relative',
  display: 'flex',
  marginBottom: 4,
  alignItems: 'center',
  height: 50,
});

const StageBar = styled(Box)<{ width: number; bgcolor: string }>(({ width, bgcolor, theme }) => ({
  height: '100%',
  width: `${Math.max(width, 5)}%`, // Minimum 5% width for visibility
  backgroundColor: bgcolor,
  borderRadius: 4,
  position: 'relative',
  transition: 'width 0.5s ease-in-out',
  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, ${alpha('#fff', 0.2)}, transparent)`,
  }
}));

const StageLabel = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: 16,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  width: 'calc(100% - 32px)',
  zIndex: 1,
  height: '100%',
}));

const StageText = styled(Typography)(({ theme }) => ({
  color: '#fff',
  fontWeight: 500,
  textShadow: '0 1px 3px rgba(0, 0, 0, 0.4)',
  fontSize: '0.875rem',
}));

const StageCount = styled(Typography)(({ theme }) => ({
  color: '#fff',
  fontWeight: 600,
  textShadow: '0 1px 3px rgba(0, 0, 0, 0.4)',
  backgroundColor: 'rgba(0, 0, 0, 0.2)',
  borderRadius: 4,
  padding: '2px 8px',
  fontSize: '0.875rem',
}));

const StatBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: theme.spacing(3),
  padding: theme.spacing(2),
  backgroundColor: alpha(theme.palette.background.default, 0.5),
  borderRadius: 8,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
}));

const StatItem = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(1, 2),
}));

const StatValue = styled(Typography)(({ theme }) => ({
  color: theme.palette.primary.main,
  fontWeight: 600,
  fontSize: '1.25rem',
}));

const StatLabel = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.75rem',
  marginTop: 4,
}));

const SalesFunnel: React.FC<SalesFunnelProps> = ({ data, totalLeads }) => {
  const theme = useTheme();
  
  // Calculate conversion rates
  const newLeadsCount = data.find(item => item.status === 'New')?.count || 0;
  const convertedCount = data.find(item => item.status === 'Converted')?.count || 0;
  const orderCount = data.find(item => item.status === 'Order')?.count || 0;
  
  const leadConversionRate = totalLeads > 0 ? (convertedCount / totalLeads) * 100 : 0;
  const orderConversionRate = convertedCount > 0 ? (orderCount / convertedCount) * 100 : 0;
  
  return (
    <FunnelContainer>
      <FunnelTitle variant="h5">Sales Funnel Analytics</FunnelTitle>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Visualizing your lead progression from initial contact to order conversion
      </Typography>
      
      {data.map((stage) => (
        <Tooltip 
          key={stage.status} 
          title={`${stage.percentage?.toFixed(1)}% of total leads (${stage.count} leads)`}
          arrow
        >
          <StageContainer>
            <StageBar width={stage.percentage || 0} bgcolor={stage.color} />
            <StageLabel>
              <StageText>{stage.status}</StageText>
              <StageCount>{stage.count}</StageCount>
            </StageLabel>
          </StageContainer>
        </Tooltip>
      ))}
      
      <StatBox>
        <StatItem>
          <StatValue>
            {totalLeads}
          </StatValue>
          <StatLabel>
            Total Leads
          </StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue>
            {leadConversionRate.toFixed(1)}%
          </StatValue>
          <StatLabel>
            Lead Conversion
          </StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue>
            {orderConversionRate.toFixed(1)}%
          </StatValue>
          <StatLabel>
            Order Rate
          </StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue>
            ₹{totalLeads > 0 ? (data.reduce((sum, item) => sum + (item.count * 10000), 0) / totalLeads).toLocaleString() : 0}
          </StatValue>
          <StatLabel>
            Avg. Budget
          </StatLabel>
        </StatItem>
      </StatBox>
    </FunnelContainer>
  );
};

export default SalesFunnel; 