import React from 'react';
import { Box, Paper, Typography, useTheme, alpha, Divider } from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import DiamondIcon from '@mui/icons-material/Diamond';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CakeIcon from '@mui/icons-material/Cake';
import FavoriteIcon from '@mui/icons-material/Favorite';
import CelebrationIcon from '@mui/icons-material/Celebration';

interface InsightItemProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: string;
}

interface MarketingInsightsProps {
  occasionBreakdown: Record<string, number>;
  totalBudget: number;
  leadCount: number;
  premiumLeads: number;
  conversionRate: number;
  monthlyGrowth: number;
}

const InsightsContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(4),
  borderRadius: 8,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: 'rgba(255, 255, 255, 0.97)',
}));

const InsightTitle = styled(Typography)(({ theme }) => ({
  fontFamily: '"Playfair Display", serif',
  marginBottom: theme.spacing(3),
  color: theme.palette.primary.dark,
  fontWeight: 600,
}));

const InsightItemContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  height: '100%',
  borderRadius: 8,
  boxShadow: 'none',
  border: `1px solid ${alpha(theme.palette.primary.light, 0.2)}`,
  background: alpha(theme.palette.background.paper, 0.7),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 6px 12px ${alpha(theme.palette.primary.main, 0.1)}`,
  },
}));

const IconBox = styled(Box)<{ bgcolor: string }>(({ bgcolor }) => ({
  width: 40,
  height: 40,
  borderRadius: 8,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: alpha(bgcolor, 0.15),
  color: bgcolor,
  marginRight: 16,
}));

const OccasionBox = styled(Box)<{ bgcolor: string }>(({ bgcolor, theme }) => ({
  borderRadius: 8,
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  backgroundColor: alpha(bgcolor, 0.1),
  border: `1px solid ${alpha(bgcolor, 0.2)}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const MetricValue = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
  lineHeight: 1.25,
}));

const MetricLabel = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  fontWeight: 500,
}));

const ChangeIndicator = styled(Box)<{ positive: boolean }>(({ positive, theme }) => ({
  display: 'flex',
  alignItems: 'center',
  color: positive ? theme.palette.success.main : theme.palette.error.main,
  fontWeight: 500,
  fontSize: '0.875rem',
  padding: '2px 6px',
  borderRadius: 4,
  backgroundColor: positive ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.error.main, 0.1),
}));

const OccasionCount = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  fontSize: '1rem',
  backgroundColor: alpha(theme.palette.common.black, 0.05),
  borderRadius: 4,
  padding: '2px 8px',
}));

const InsightItem: React.FC<InsightItemProps> = ({ title, value, change, icon, color }) => {
  return (
    <InsightItemContainer>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <IconBox bgcolor={color}>
          {icon}
        </IconBox>
        <MetricLabel>
          {title}
        </MetricLabel>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between' }}>
        <MetricValue>
          {value}
        </MetricValue>
        {change !== undefined && (
          <ChangeIndicator positive={change >= 0}>
            {change >= 0 ? 
              <ArrowUpwardIcon fontSize="small" /> : 
              <ArrowDownwardIcon fontSize="small" />
            }
            {Math.abs(change)}%
          </ChangeIndicator>
        )}
      </Box>
    </InsightItemContainer>
  );
};

const MarketingInsights: React.FC<MarketingInsightsProps> = ({ 
  occasionBreakdown, 
  totalBudget, 
  leadCount,
  premiumLeads,
  conversionRate,
  monthlyGrowth
}) => {
  const theme = useTheme();
  
  const getOccasionIcon = (occasion: string) => {
    switch (occasion) {
      case 'wedding':
        return <FavoriteIcon />;
      case 'birthday':
        return <CakeIcon />;
      case 'engagement':
        return <DiamondIcon />;
      case 'festival':
        return <CelebrationIcon />;
      default:
        return <DiamondIcon />;
    }
  };
  
  const getOccasionColor = (occasion: string) => {
    switch (occasion) {
      case 'wedding':
        return theme.palette.error.main;
      case 'birthday':
        return theme.palette.info.main;
      case 'engagement':
        return theme.palette.secondary.main;
      case 'festival':
        return theme.palette.warning.main;
      case 'daily_wear':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };
  
  return (
    <InsightsContainer>
      <InsightTitle variant="h5">Marketing Dashboard Insights</InsightTitle>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
        <Box sx={{ width: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
          <InsightItem 
            title="Total Leads" 
            value={leadCount}
            change={monthlyGrowth}
            icon={<TrendingUpIcon />}
            color={theme.palette.primary.main}
          />
        </Box>
        <Box sx={{ width: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
          <InsightItem 
            title="Total Budget" 
            value={`₹${totalBudget.toLocaleString()}`}
            icon={<DiamondIcon />}
            color={theme.palette.secondary.main}
          />
        </Box>
        <Box sx={{ width: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
          <InsightItem 
            title="Premium Leads" 
            value={premiumLeads}
            change={8.2}
            icon={<DiamondIcon />}
            color="#c9b037"
          />
        </Box>
        <Box sx={{ width: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
          <InsightItem 
            title="Conversion Rate" 
            value={`${conversionRate.toFixed(1)}%`}
            change={-2.5}
            icon={<TrendingUpIcon />}
            color={theme.palette.success.main}
          />
        </Box>
      </Box>
      
      <Divider sx={{ my: 3 }} />
      
      <Typography variant="h6" sx={{ mb: 2, fontFamily: '"Playfair Display", serif' }}>
        Occasion Breakdown
      </Typography>
      
      {Object.entries(occasionBreakdown).map(([occasion, count]) => (
        <OccasionBox key={occasion} bgcolor={getOccasionColor(occasion)}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconBox bgcolor={getOccasionColor(occasion)}>
              {getOccasionIcon(occasion)}
            </IconBox>
            <Typography variant="body1" sx={{ ml: 1, textTransform: 'capitalize', fontWeight: 500 }}>
              {occasion.replace('_', ' ')}
            </Typography>
          </Box>
          <OccasionCount>
            {count} leads
          </OccasionCount>
        </OccasionBox>  
      ))}
    </InsightsContainer>
  );
};

export default MarketingInsights; 