import React from 'react';
import { AppBar, Toolbar, Typography, Container, Box, Button, alpha, useTheme, Menu, MenuItem, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import useAuthStore from '../../store/authStore';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';

interface LayoutProps {
  children: React.ReactNode;
}

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.primary.dark, 0.95),
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  borderBottom: `1px solid ${alpha(theme.palette.primary.light, 0.2)}`,
}));

const StyledToolbar = styled(Toolbar)({
  display: 'flex',
  justifyContent: 'space-between',
});

const Logo = styled(Typography)(({ theme }) => ({
  fontFamily: '"Playfair Display", serif',
  fontWeight: 600,
  letterSpacing: 1,
  background: `-webkit-linear-gradient(45deg, ${theme.palette.primary.light}, #ffffff)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
}));

const NavButtons = styled(Box)({
  display: 'flex',
  gap: '8px',
});

const Footer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: alpha(theme.palette.primary.dark, 0.95),
  color: theme.palette.common.white,
  textAlign: 'center',
  borderTop: `1px solid ${alpha(theme.palette.primary.light, 0.2)}`,
  marginTop: 'auto',
}));

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  
  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    logout();
    navigate('/login');
  };

  // Only show nav buttons for the user's role
  const showSalesNav = !user || user.role === 'sales';
  const showMarketingNav = !user || user.role === 'marketing';
  const showProductionNav = !user || user.role === 'production';
  
  const buttonStyle = {
    color: '#fff',
    fontFamily: '"Cormorant Garamond", serif',
    fontSize: '1rem',
    padding: '6px 16px',
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.light, 0.2),
    },
  };
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <StyledAppBar position="static">
        <StyledToolbar>
          <Logo variant="h6">
            SYMETREE
          </Logo>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <NavButtons>
              {showSalesNav && (
                <Button
                  component={RouterLink}
                  to="/"
                  sx={buttonStyle}
                >
                  Sales
                </Button>
              )}
              
              {showMarketingNav && (
                <Button
                  component={RouterLink}
                  to="/marketing"
                  sx={buttonStyle}
                >
                  Marketing
                </Button>
              )}
              
              {showProductionNav && (
                <Button
                  component={RouterLink}
                  to="/production"
                  sx={buttonStyle}
                >
                  Production
                </Button>
              )}
            </NavButtons>
            
            {user && (
              <>
                <IconButton
                  size="large"
                  edge="end"
                  aria-label="account"
                  aria-haspopup="true"
                  onClick={handleProfileMenuOpen}
                  color="inherit"
                  sx={{ ml: 2 }}
                >
                  <AccountCircleIcon />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                >
                  <MenuItem disabled>
                    <Typography variant="body2">
                      {user.username} ({user.role})
                    </Typography>
                  </MenuItem>
                  <MenuItem onClick={handleLogout}>
                    <LogoutIcon fontSize="small" sx={{ mr: 1 }} />
                    Logout
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </StyledToolbar>
      </StyledAppBar>
      <Container sx={{ flexGrow: 1, py: 4 }} maxWidth="lg">
        {children}
      </Container>
      <Footer>
        <Typography variant="body2" sx={{ fontFamily: '"Cormorant Garamond", serif' }}>
          © {new Date().getFullYear()} Symetree Jewelry • Crafting Elegance
        </Typography>
      </Footer>
    </Box>
  );
};

export default Layout; 