import React from 'react';
import { Box } from '@mui/material';
import symetreeRing from '../../assets/images/symetree-ring.png';

interface JewelryBackgroundProps {
  children: React.ReactNode;
}

const JewelryBackground: React.FC<JewelryBackgroundProps> = ({ children }) => {
  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 3,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${symetreeRing})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.15,
          zIndex: -1,
        },
      }}
    >
      {children}
    </Box>
  );
};

export default JewelryBackground; 