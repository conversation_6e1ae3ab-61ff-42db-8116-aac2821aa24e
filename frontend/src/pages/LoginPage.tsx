import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  <PERSON>ton, 
  TextField, 
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
  styled,
  <PERSON><PERSON><PERSON><PERSON>,
  Alert as <PERSON><PERSON><PERSON><PERSON>t
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';
// Import logo and optional icons
// Import a jewelry image
import jewelryImage from '../assets/images/symetree-ring.png'; // Use existing image or add your own

// Correctly styled components
const LoginContainer = styled(Box)`
  display: flex;
  min-height: 100vh;
`;

const ImageContainer = styled(Box)`
  flex: 1;
  display: none;
  position: relative;
  overflow: hidden;
  
  @media (min-width: 900px) {
    display: block;
  }
`;

const FormContainer = styled(Box)`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  background-color: #ffffff;
`;

const LogoImage = styled('img')`
  max-width: 180px;
  height: auto;
  margin-bottom: 48px;
`;

const StyledTab = styled(Tab)`
  font-size: 0.9rem;
  font-weight: 500;
  
  &.Mui-selected {
    color: #c9b037;
    font-weight: 600;
  }
`;

const LoginTitle = styled(Typography)`
  font-size: 2.5rem;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 32px;
`;

const StyledButton = styled(Button)`
  margin-top: 24px;
  padding: 12px;
  font-size: 1rem;
  background-color: #c9b037;
  width: 100%;
  border-radius: 4px;
  text-transform: none;
  
  &:hover {
    background-color: #8a7534;
  }
`;

const LoginPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isAuthenticated, loading, error, user } = useAuthStore();
  const navigate = useNavigate();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMsg, setSnackbarMsg] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success'|'error'>('error');

  useEffect(() => {
    if (error) {
      setSnackbarMsg(error);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  }, [error]);

  useEffect(() => {
    // Debug: log user and error state
    console.log('Auth user:', user, 'isAuthenticated:', isAuthenticated, 'error:', error);
    // If already authenticated and user/role is set, redirect based on role
    if (isAuthenticated && user && user.role) {
      setSnackbarMsg('Login successful! Redirecting...');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setTimeout(() => {
        if (user.role === 'sales') navigate('/');
        else if (user.role === 'marketing') navigate('/marketing');
        else if (user.role === 'production') navigate('/production');
      }, 800);
    }
  }, [isAuthenticated, user, navigate]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    // Set default credentials based on selected role
    if (newValue === 0) setUsername('sales');
    else if (newValue === 1) setUsername('marketing');
    else if (newValue === 2) setUsername('production');
    setPassword('');
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    // Always trim username to avoid whitespace issues
    let userToLogin = username.trim();
    if (!userToLogin) {
      if (tabValue === 0) userToLogin = 'sales';
      else if (tabValue === 1) userToLogin = 'marketing';
      else if (tabValue === 2) userToLogin = 'production';
    }
    console.log('Logging in as:', userToLogin, password); // Debug log
    await login(userToLogin, password);
  };

  // Handle password visibility toggle
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <LoginContainer>
      {/* Left side image */}
      <ImageContainer>
        <Box
          component="img"
          src={jewelryImage}
          alt="Symetree Jewelry"
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      </ImageContainer>

      {/* Right side form */}
      <FormContainer>
        <Box sx={{ 
          width: '100%', 
          maxWidth: 400,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
          
          <LoginTitle variant="h1">Login</LoginTitle>

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              width: '100%',
              mb: 4,
              '& .MuiTabs-indicator': {
                backgroundColor: '#c9b037',
              },
            }}
          >
            <StyledTab label="Sales" />
            <StyledTab label="Marketing" />
            <StyledTab label="Production" />
          </Tabs>

          <form onSubmit={handleLogin} style={{ width: '100%' }}>
            <TextField
              label="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value.trim())}
              variant="outlined"
              fullWidth
              margin="normal"
              sx={{ mb: 3 }}
            />

            <TextField
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              variant="outlined"
              fullWidth
              margin="normal"
              sx={{ mb: 2 }}
            />

            {error && (
              <Typography color="error" variant="body2" sx={{ mt: 1, mb: 1 }}>
                {error}
              </Typography>
            )}

            <StyledButton
              type="submit"
              variant="contained"
              disabled={loading || !password}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'Login'}
            </StyledButton>

            <Box sx={{ mt: 4, width: '100%' }}>
              <Typography align="center" variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Demo Credentials
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                <Box sx={{ width: '32%', textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    <strong>Sales</strong><br />
                    sales / sales123
                  </Typography>
                </Box>
                <Box sx={{ width: '32%', textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    <strong>Marketing</strong><br />
                    marketing / marketing123
                  </Typography>
                </Box>
                <Box sx={{ width: '32%', textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    <strong>Production</strong><br />
                    production / production123
                  </Typography>
                </Box>
              </Box>
            </Box>
          </form>
        </Box>
        <Snackbar open={snackbarOpen} autoHideDuration={4000} onClose={handleSnackbarClose}>
          <MuiAlert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
            {snackbarMsg}
          </MuiAlert>
        </Snackbar>
      </FormContainer>
    </LoginContainer>
  );
};

export default LoginPage;