import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Paper,
  Box,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  CircularProgress,
  Stack,
  Tabs,
  Tab,
  Input,
  Alert,
  MenuItem,
  Select,
  InputLabel,
  SelectChangeEvent,
} from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { getLeads, createOrderReview, uploadCadFile } from '../api/api';
import { styled } from '@mui/material/styles';

interface CatalogueItem {
  id: number;
  image: string;
  name?: string;
  price?: number;
}

// We'll use MuiGrid directly instead of creating a custom component

interface Lead {
  id: number;
  name: string;
  budget: number;
  occasion: string;
  timeline: string;
  remarks: string;
  design_reference: string | null;
  design_file: string | null;
  catalogue_selection: CatalogueItem[];
  jewelry_categories?: string;
}

// Styled components for a more sober, professional look
const PageTitle = styled(Typography)(({ theme }) => ({
  fontFamily: 'Arial, sans-serif',
  color: '#333333',
  fontWeight: 600,
  fontSize: '1.75rem',
  marginBottom: theme.spacing(3),
}));

const ProductionCard = styled(Card)(({ theme }) => ({
  borderRadius: 8,
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
  border: '1px solid #e0e0e0',
  transition: 'box-shadow 0.3s ease',
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.12)',
  }
}));

const OrderButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#555555',
  color: '#ffffff',
  borderRadius: 4,
  padding: '8px 16px',
  textTransform: 'none',
  '&:hover': {
    backgroundColor: '#444444',
  }
}));

// Improved ImagePreview component with better error handling
const ImagePreview: React.FC<{ src: string | null | undefined | CatalogueItem }> = ({ src }) => {
  const [open, setOpen] = useState(false);
  const [error, setError] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!src) {
      setError(true);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(false);

    // Fix URL construction
    let finalUrl = '';

    // Check if src is a CatalogueItem object
    if (typeof src === 'object' && src !== null && 'image' in src) {
      // Get the image path from the catalogue item
      finalUrl = src.image;
      console.log('Catalogue item image URL:', finalUrl);
    } else if (typeof src === 'string') {
      finalUrl = src;
      console.log('String image URL:', finalUrl);
    } else {
      console.error('Invalid image source type:', src);
      setError(true);
      setLoading(false);
      return;
    }

    // Fix URL for relative paths
    if (finalUrl && !finalUrl.startsWith('http') && !finalUrl.startsWith('data:')) {
      // Ensure the URL is properly constructed for server paths
      finalUrl = `http://localhost:8000${finalUrl.startsWith('/') ? '' : '/'}${finalUrl}`;
      console.log('Constructed URL:', finalUrl);
    }

    setImageUrl(finalUrl);

    // Pre-load the image to check for errors
    const img = new Image();
    img.onload = () => {
      console.log('Image loaded successfully:', finalUrl);
      setLoading(false);
    };
    img.onerror = (e) => {
      console.error('Error loading image:', finalUrl, e);
      setError(true);
      setLoading(false);
    };
    img.src = finalUrl;
  }, [src]);

  // Show loading indicator while image is loading
  if (loading) {
    return (
      <Box
        sx={{
          width: 100,
          height: 100,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          backgroundColor: '#f8f8f8',
        }}
      >
        <CircularProgress size={24} sx={{ color: "#666666" }} />
      </Box>
    );
  }

  // If no src or error loading, show placeholder
  if (!src || error) {
    return (
      <Box
        sx={{
          width: 100,
          height: 100,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          backgroundColor: '#f8f8f8',
          fontSize: '1rem',
          color: '#666666',
          fontFamily: 'Arial, sans-serif',
        }}
      >
        <ImageIcon sx={{ fontSize: '2rem', color: '#666666', opacity: 0.6 }} />
      </Box>
    );
  }

  return (
    <>
      <Box
        component="img"
        src={imageUrl}
        sx={{
          width: 100,
          height: 100,
          objectFit: 'cover',
          cursor: 'pointer',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          transition: 'all 0.2s ease',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            transform: 'scale(1.02)'
          }
        }}
        onClick={() => setOpen(true)}
        alt="Design reference"
      />
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <Box component="img" src={imageUrl} sx={{ width: '100%', maxHeight: '80vh', objectFit: 'contain' }} />
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpen(false)} sx={{ color: '#555555' }}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

const ProductionPage: React.FC = () => {
  const [orders, setOrders] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [openReview, setOpenReview] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [reviewData, setReviewData] = useState({
    approved: true,
    estimated_cost: '',
    estimated_delivery: null as Date | null,
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // CAD upload state
  const [cadFile, setCadFile] = useState<File | null>(null);
  const [cadUploadSuccess, setCadUploadSuccess] = useState<string | null>(null);
  const [cadUploadError, setCadUploadError] = useState<string | null>(null);
  const [cadUploading, setCadUploading] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<number | "">("");

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await getLeads({ status: 'order' });
      setOrders(response.results || []);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReviewOpen = (lead: Lead) => {
    setSelectedLead(lead);
    setReviewData({
      approved: true,
      estimated_cost: lead.budget.toString(),
      estimated_delivery: new Date(),
      notes: ''
    });
    setOpenReview(true);
  };

  const handleReviewClose = () => {
    setOpenReview(false);
    setSelectedLead(null);
  };

  const handleReviewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setReviewData(prev => ({
      ...prev,
      [name]: name === 'approved' ? value === 'true' : value
    }));
  };

  const handleDateChange = (date: unknown) => {
    setReviewData(prev => ({
      ...prev,
      estimated_delivery: date as Date | null
    }));
  };

  const handleReviewSubmit = async () => {
    if (!selectedLead || !reviewData.estimated_delivery) return;

    try {
      setSubmitting(true);

      await createOrderReview({
        lead: selectedLead.id,
        approved: reviewData.approved,
        estimated_cost: parseInt(reviewData.estimated_cost),
        estimated_delivery: reviewData.estimated_delivery.toISOString().split('T')[0],
        notes: reviewData.notes
      });

      fetchOrders(); // Refresh the list
      handleReviewClose();
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Tab change handler
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // CAD file upload handlers
  const handleCadFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setCadFile(e.target.files[0]);
    }
  };

  const handleOrderSelect = (event: SelectChangeEvent<number>) => {
    setSelectedOrderId(Number(event.target.value));
  };

  const handleCadUpload = async () => {
    if (!cadFile || !selectedOrderId) {
      setCadUploadError('Please select an order and a CAD file.');
      return;
    }
    setCadUploading(true);
    setCadUploadError(null);
    setCadUploadSuccess(null);

    try {
      await uploadCadFile(selectedOrderId, cadFile);
      setCadUploadSuccess('CAD file uploaded and attached to order successfully!');
      setCadFile(null);
      setSelectedOrderId("");
    } catch (err) {
      setCadUploadError('Failed to upload CAD file. Please try again.');
    } finally {
      setCadUploading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress sx={{ color: '#555555' }} />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 2 }}>
        <PageTitle>Production Dashboard</PageTitle>
        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Order Reviews" />
          <Tab label="Upload CAD Files" />
        </Tabs>

        {tabValue === 0 && (
          orders.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center', bgcolor: '#f8f8f8', border: '1px solid #e0e0e0', borderRadius: 2 }}>
              <Typography sx={{ color: '#666666', fontFamily: 'Arial, sans-serif' }}>No orders available for review</Typography>
            </Paper>
          ) : (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mt: 2 }}>
              {orders.map((order) => (
                <Box key={order.id} sx={{ width: { xs: '100%', md: '47%' }, mb: 3 }}>
                  <ProductionCard>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ color: '#333333', fontFamily: 'Arial, sans-serif', fontWeight: 600 }}>
                        {order.name}
                      </Typography>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body1" sx={{ color: '#444444', fontFamily: 'Arial, sans-serif', mb: 0.5 }}>
                          Budget: ₹{order.budget.toLocaleString()}
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#444444', fontFamily: 'Arial, sans-serif', mb: 0.5 }}>
                          Occasion: {order.occasion === 'wedding' ? 'Wedding' :
                                    order.occasion === 'special_occasions' ? 'Special occasions' :
                                    order.occasion === 'casual' ? 'Casual' :
                                    order.occasion === 'other' ? 'Other' : order.occasion}
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#444444', fontFamily: 'Arial, sans-serif', mb: 0.5 }}>
                          Timeline: {new Date(order.timeline).toLocaleDateString()}
                        </Typography>

                        {order.remarks && (
                          <Typography variant="body2" sx={{ color: '#666666', fontFamily: 'Arial, sans-serif', mt: 1 }}>
                            Remarks: {order.remarks}
                          </Typography>
                        )}
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" gutterBottom sx={{ color: '#444444', fontFamily: 'Arial, sans-serif', fontWeight: 600 }}>
                          Design Reference
                        </Typography>

                        {order.design_reference ? (
                          <Box
                            component="img"
                            src={order.design_reference}
                            alt="Design Reference"
                            sx={{ maxWidth: '100%', height: 'auto', maxHeight: 200, borderRadius: 1 }}
                          />
                        ) : order.design_file ? (
                          <Box
                            component="img"
                            src={order.design_file}
                            alt="Design File"
                            sx={{ maxWidth: '100%', height: 'auto', maxHeight: 200, borderRadius: 1 }}
                          />
                        ) : order.catalogue_selection && order.catalogue_selection.length > 0 ? (
                          <Box>
                            <Typography sx={{ mb: 1, color: '#444444', fontFamily: 'Arial, sans-serif' }}>
                              Selected {order.catalogue_selection.length} items from catalogue:
                            </Typography>
                            <Stack direction="row" spacing={2} sx={{ mt: 1, flexWrap: 'wrap', gap: 2 }}>
                              {order.catalogue_selection.map((item) => (
                                <Box key={item.id} sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  gap: 1
                                }}>
                                  <ImagePreview src={item} />
                                  {item.name && <Typography variant="caption" sx={{ textAlign: 'center', color: '#555555', fontFamily: 'Arial, sans-serif' }}>{item.name}</Typography>}
                                  {item.price && <Typography variant="caption" sx={{ textAlign: 'center', fontWeight: 'bold', color: '#444444', fontFamily: 'Arial, sans-serif' }}>₹{item.price.toLocaleString()}</Typography>}
                                </Box>
                              ))}
                            </Stack>
                          </Box>
                        ) : (
                          <Typography sx={{ color: '#777777', fontFamily: 'Arial, sans-serif' }}>
                            No design reference provided
                          </Typography>
                        )}
                      </Box>

                      <OrderButton
                        variant="contained"
                        onClick={() => handleReviewOpen(order)}
                        fullWidth
                      >
                        Review Order
                      </OrderButton>
                    </CardContent>
                  </ProductionCard>
                </Box>
              ))}
            </Box>
          )
        )}

        {tabValue === 1 && (
          <Paper sx={{ p: 4, maxWidth: 500, mx: 'auto', mt: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontFamily: 'Arial, sans-serif' }}>
              Upload CAD File for Order
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="order-select-label">Select Order</InputLabel>
              <Select
                labelId="order-select-label"
                value={selectedOrderId}
                label="Select Order"
                onChange={handleOrderSelect}
                disabled={cadUploading}
              >
                {orders.map(order => (
                  <MenuItem key={order.id} value={order.id}>
                    {order.name} (#{order.id})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Input
              type="file"
              inputProps={{ accept: '.stl,.obj,.step,.iges,.dwg,.dxf,.zip' }}
              onChange={handleCadFileChange}
              disabled={cadUploading}
              fullWidth
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              onClick={handleCadUpload}
              disabled={cadUploading || !cadFile || !selectedOrderId}
              sx={{ backgroundColor: '#555555', color: '#fff', textTransform: 'none' }}
              fullWidth
            >
              {cadUploading ? 'Uploading...' : 'Upload'}
            </Button>
            {cadUploadSuccess && (
              <Alert severity="success" sx={{ mt: 2 }}>{cadUploadSuccess}</Alert>
            )}
            {cadUploadError && (
              <Alert severity="error" sx={{ mt: 2 }}>{cadUploadError}</Alert>
            )}
          </Paper>
        )}

        {/* Review Dialog */}
        <Dialog 
          open={openReview} 
          onClose={handleReviewClose} 
          maxWidth="md" 
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            }
          }}
        >
          <DialogTitle sx={{ fontFamily: 'Arial, sans-serif', color: '#333333', borderBottom: '1px solid #e0e0e0', px: 3, py: 2 }}>
            Review Order
          </DialogTitle>
          <DialogContent sx={{ px: 3, py: 2 }}>
            {selectedLead && (
              <Box sx={{ pt: 1 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontFamily: 'Arial, sans-serif', color: '#333333', fontWeight: 600 }}>
                  {selectedLead.name} - {selectedLead.occasion === 'wedding' ? 'Wedding' :
                                        selectedLead.occasion === 'special_occasions' ? 'Special occasions' :
                                        selectedLead.occasion === 'casual' ? 'Casual' :
                                        selectedLead.occasion === 'other' ? 'Other' : selectedLead.occasion}
                </Typography>

                {/* Display design reference or catalogue items */}
                <Box sx={{ mb: 3, mt: 1 }}>
                  {selectedLead.design_reference ? (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontFamily: 'Arial, sans-serif', color: '#444444' }}>Design Reference:</Typography>
                      <Box
                        component="img"
                        src={selectedLead.design_reference}
                        alt="Design Reference"
                        sx={{ maxWidth: '100%', height: 'auto', maxHeight: 200, borderRadius: 1, border: '1px solid #e0e0e0' }}
                      />
                    </>
                  ) : selectedLead.design_file ? (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontFamily: 'Arial, sans-serif', color: '#444444' }}>Design File:</Typography>
                      <Box
                        component="img"
                        src={selectedLead.design_file}
                        alt="Design File"
                        sx={{ maxWidth: '100%', height: 'auto', maxHeight: 200, borderRadius: 1, border: '1px solid #e0e0e0' }}
                      />
                    </>
                  ) : selectedLead.catalogue_selection && selectedLead.catalogue_selection.length > 0 ? (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontFamily: 'Arial, sans-serif', color: '#444444' }}>Catalogue Selections:</Typography>
                      <Stack direction="row" spacing={2} sx={{ mt: 1, flexWrap: 'wrap', gap: 2 }}>
                        {selectedLead.catalogue_selection.map((item) => (
                          <Box key={item.id} sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            gap: 1
                          }}>
                            <ImagePreview src={item} />
                            {item.name && <Typography variant="caption" sx={{ textAlign: 'center', fontFamily: 'Arial, sans-serif', color: '#555555' }}>{item.name}</Typography>}
                            {item.price && <Typography variant="caption" sx={{ textAlign: 'center', fontWeight: 'bold', fontFamily: 'Arial, sans-serif', color: '#444444' }}>₹{item.price.toLocaleString()}</Typography>}
                          </Box>
                        ))}
                      </Stack>
                    </>
                  ) : null}
                </Box>

                <FormControl component="fieldset" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontFamily: 'Arial, sans-serif', color: '#444444', mb: 1 }}>Order Approval</Typography>
                  <RadioGroup
                    name="approved"
                    value={reviewData.approved.toString()}
                    onChange={handleReviewChange}
                    row
                  >
                    <FormControlLabel
                      value="true"
                      control={<Radio sx={{ color: '#555555', '&.Mui-checked': { color: '#555555' } }} />}
                      label={<Typography sx={{ fontFamily: 'Arial, sans-serif', color: '#444444' }}>Approve</Typography>}
                    />
                    <FormControlLabel
                      value="false"
                      control={<Radio sx={{ color: '#555555', '&.Mui-checked': { color: '#555555' } }} />}
                      label={<Typography sx={{ fontFamily: 'Arial, sans-serif', color: '#444444' }}>Reject</Typography>}
                    />
                  </RadioGroup>
                </FormControl>

                <TextField
                  label="Estimated Cost (₹)"
                  name="estimated_cost"
                  type="number"
                  value={reviewData.estimated_cost}
                  onChange={handleReviewChange}
                  fullWidth
                  margin="normal"
                  sx={{ 
                    '& .MuiInputLabel-root': { fontFamily: 'Arial, sans-serif', color: '#555555' },
                    '& .MuiOutlinedInput-root': { '& fieldset': { borderColor: '#d0d0d0' } }
                  }}
                />

                <Box sx={{ my: 2 }}>
                  <DatePicker
                    label="Estimated Delivery Date"
                    value={reviewData.estimated_delivery}
                    onChange={handleDateChange}
                    renderInput={(params) => (
                      <TextField 
                        {...params} 
                        fullWidth 
                        sx={{ 
                          '& .MuiInputLabel-root': { fontFamily: 'Arial, sans-serif', color: '#555555' },
                          '& .MuiOutlinedInput-root': { '& fieldset': { borderColor: '#d0d0d0' } }
                        }}
                      />
                    )}
                  />
                </Box>

                <TextField
                  label="Production Notes"
                  name="notes"
                  value={reviewData.notes}
                  onChange={handleReviewChange}
                  multiline
                  rows={4}
                  fullWidth
                  margin="normal"
                  sx={{ 
                    '& .MuiInputLabel-root': { fontFamily: 'Arial, sans-serif', color: '#555555' },
                    '& .MuiOutlinedInput-root': { '& fieldset': { borderColor: '#d0d0d0' } }
                  }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
            <Button 
              onClick={handleReviewClose} 
              disabled={submitting}
              sx={{ 
                color: '#555555', 
                textTransform: 'none',
                fontFamily: 'Arial, sans-serif'
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReviewSubmit}
              variant="contained"
              disabled={submitting}
              sx={{ 
                backgroundColor: '#555555', 
                color: '#ffffff',
                '&:hover': {
                  backgroundColor: '#444444'
                },
                textTransform: 'none',
                fontFamily: 'Arial, sans-serif'
              }}
            >
              {submitting ? 'Submitting...' : 'Submit Review'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default ProductionPage;