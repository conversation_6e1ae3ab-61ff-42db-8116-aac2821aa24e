import React, { useState, useEffect } from 'react';
import {
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Box,
  Chip,
  SelectChangeEvent,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Stack,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { getLeads, updateLeadStatus } from '../api/api';
// Add Material UI icons
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import ReplayIcon from '@mui/icons-material/Replay';
import PhoneIcon from '@mui/icons-material/Phone';
import AddAlertIcon from '@mui/icons-material/AddAlert';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import StoreIcon from '@mui/icons-material/Store';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import CategoryIcon from '@mui/icons-material/Category';
import ImageIcon from '@mui/icons-material/Image';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import DiamondIcon from '@mui/icons-material/Diamond';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import FavoriteIcon from '@mui/icons-material/Favorite';
import CakeIcon from '@mui/icons-material/Cake';
import CelebrationIcon from '@mui/icons-material/Celebration';
import LocalMallIcon from '@mui/icons-material/LocalMall';
import GridViewIcon from '@mui/icons-material/GridView';

// Define the Lead interface with proper typing for catalogue_selection
interface CatalogueItem {
  id: number;
  image: string;
  name?: string;
  price?: number;
}

// Define the Lead interface
interface Lead {
  id: number;
  name: string;
  phone: string;
  budget: number;
  occasion: string;
  timeline: string;
  status: string;
  remarks: string;
  created_at: string;
  updated_at: string;
  design_reference: string | null;
  jewelry_categories: string;
  store: string;
  catalogue_selection?: CatalogueItem[] | null;
}

// Define interface for segmentation tags
interface SegmentationTag {
  label: string;
  color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

// Interface for funnel data item
interface FunnelDataItem {
  status: string;
  count: number;
  color: string;
  percentage: number;
}

// Interface for insights data
interface InsightsData {
  occasionBreakdown: Record<string, number>;
  totalBudget: number;
  leadCount: number;
  premiumLeads: number;
  conversionRate: number;
  monthlyGrowth: number;
}

// UPDATED STYLES WITH NEW COLOR SCHEME
// Styled components with a new blue/teal theme instead of gold
const PageTitle = styled(Typography)(({ theme }) => ({
  fontFamily: '"Poppins", "Roboto", sans-serif',
  color: '#2c3e50',
  fontWeight: 600,
  fontSize: '2.6rem',
  marginBottom: theme.spacing(3),
}));

const FilterContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: 12,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  background: '#ffffff',
  border: '1px solid rgba(41, 128, 185, 0.1)',
}));

const FilterTitle = styled(Typography)(({ theme }) => ({
  fontFamily: '"Poppins", "Roboto", sans-serif',
  fontWeight: 600,
  fontSize: '1.4rem',
  marginBottom: theme.spacing(2),
  color: '#2980b9',
}));

// Extend the ActionButton with ButtonProps
const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: 8,
  padding: '10px 24px',
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '1rem',
  boxShadow: '0 4px 12px rgba(41, 128, 185, 0.2)',
  background: 'linear-gradient(45deg, #2980b9 0%, #3498db 100%)',
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  color: '#fff',
  minWidth: '130px',
  whiteSpace: 'nowrap',
  '&:hover': {
    background: 'linear-gradient(45deg, #3498db 0%, #5dade2 100%)',
    boxShadow: '0 6px 16px rgba(41, 128, 185, 0.3)',
    transform: 'translateY(-1px)',
    transition: 'all 0.2s',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

const SecondaryButton = styled(Button)(({ theme }) => ({
  borderRadius: 8,
  padding: '10px 24px',
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '1rem',
  border: '1px solid #3498db',
  color: '#3498db',
  background: 'transparent',
  '&:hover': {
    background: 'rgba(52, 152, 219, 0.05)',
    borderColor: '#2980b9',
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: 12,
  overflow: 'auto',
  border: '1px solid rgba(41, 128, 185, 0.1)',
  maxWidth: '100%',
  maxHeight: '1200px',
  '&::-webkit-scrollbar': {
    width: '10px',
    height: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: 'rgba(41, 128, 185, 0.3)',
    borderRadius: '5px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: 'rgba(41, 128, 185, 0.05)',
  }
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  background: 'linear-gradient(to right, rgba(41, 128, 185, 0.05), rgba(41, 128, 185, 0.1))',
  '& th': {
    fontWeight: 600,
    color: '#2980b9',
    fontSize: '1.1rem',
    padding: '18px',
    borderBottom: '1px solid rgba(41, 128, 185, 0.1)',
    fontFamily: '"Poppins", "Roboto", sans-serif',
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(even)': {
    backgroundColor: 'rgba(249, 249, 246, 0.6)',
  },
  '&:hover': {
    backgroundColor: 'rgba(41, 128, 185, 0.05)',
  },
  '& td': {
    borderColor: 'rgba(41, 128, 185, 0.1)',
    padding: '16px 18px',
    fontSize: '1.1rem',
    fontFamily: '"Poppins", "Roboto", sans-serif',
  }
}));

// Insights card styled components
const InsightsCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: 12,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  background: '#ffffff',
  height: '100%',
  border: '1px solid rgba(41, 128, 185, 0.1)',
  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 8px 24px rgba(41, 128, 185, 0.15)',
  }
}));

const InsightTitle = styled(Typography)({
  fontFamily: '"Poppins", "Roboto", sans-serif',
  color: '#2980b9',
  fontWeight: 600,
  fontSize: '1.5rem',
  marginBottom: 16,
});

const InsightValue = styled(Typography)({
  fontSize: '2.2rem',
  fontWeight: 600,
  color: '#2c3e50',
  fontFamily: '"Poppins", "Roboto", sans-serif',
});

const InsightLabel = styled(Typography)({
  fontSize: '1.1rem',
  color: '#666',
  fontFamily: '"Poppins", "Roboto", sans-serif',
});

const InsightIcon = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'bgcolor'
})<{ bgcolor: string }>(({ bgcolor }) => ({
  width: 60,
  height: 60,
  borderRadius: 12,
  backgroundColor: bgcolor,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: '#fff',
  marginBottom: 2,
}));

// Add Tab Panel Component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

// Improved ImagePreview component with better error handling
const ImagePreview: React.FC<{ src: string | null | undefined | CatalogueItem }> = ({ src }) => {
  const [open, setOpen] = useState(false);
  const [error, setError] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!src) {
      setError(true);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(false);

    // Fix URL construction
    let finalUrl = '';

    // Check if src is a CatalogueItem object
    if (typeof src === 'object' && src !== null && 'image' in src) {
      // Get the image path from the catalogue item
      finalUrl = src.image;
      console.log('Catalogue item image URL:', finalUrl);
    } else if (typeof src === 'string') {
      finalUrl = src;
      console.log('String image URL:', finalUrl);
    } else {
      console.error('Invalid image source type:', src);
      setError(true);
      setLoading(false);
      return;
    }

    // Fix URL for relative paths
    if (finalUrl && !finalUrl.startsWith('http') && !finalUrl.startsWith('data:')) {
      // Ensure the URL is properly constructed for server paths
      finalUrl = `http://localhost:8000${finalUrl.startsWith('/') ? '' : '/'}${finalUrl}`;
      console.log('Constructed URL:', finalUrl);
    }

    setImageUrl(finalUrl);

    // Pre-load the image to check for errors
    const img = new Image();
    img.onload = () => {
      console.log('Image loaded successfully:', finalUrl);
      setLoading(false);
    };
    img.onerror = (e) => {
      console.error('Error loading image:', finalUrl, e);
      setError(true);
      setLoading(false);
    };
    img.src = finalUrl;
  }, [src]);

  // Show loading indicator while image is loading
  if (loading) {
    return (
      <Box
        sx={{
          width: 100,
          height: 100,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid rgba(41, 128, 185, 0.2)',
          borderRadius: 2,
          backgroundColor: 'rgba(249, 249, 246, 0.6)',
        }}
      >
        <CircularProgress size={24} sx={{ color: "#3498db" }} />
      </Box>
    );
  }

  // If no src or error loading, show placeholder
  if (!src || error) {
    return (
      <Box
        sx={{
          width: 100,
          height: 100,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid rgba(41, 128, 185, 0.2)',
          borderRadius: 2,
          backgroundColor: 'rgba(249, 249, 246, 0.6)',
          fontSize: '1rem',
          color: '#3498db',
          fontFamily: '"Poppins", "Roboto", sans-serif',
        }}
      >
        <ImageIcon sx={{ fontSize: '2rem', color: '#3498db', opacity: 0.6 }} />
      </Box>
    );
  }

  return (
    <>
      <Box
        component="img"
        src={imageUrl}
        sx={{
          width: 100,
          height: 100,
          objectFit: 'cover',
          cursor: 'pointer',
          border: '1px solid rgba(41, 128, 185, 0.2)',
          borderRadius: 2,
          transition: 'all 0.2s ease',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(41, 128, 185, 0.2)',
            transform: 'scale(1.05)'
          }
        }}
        onClick={() => setOpen(true)}
        alt="Design reference"
      />
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <Box component="img" src={imageUrl} sx={{ width: '100%', maxHeight: '80vh', objectFit: 'contain' }} />
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <SecondaryButton
            onClick={() => setOpen(false)}
            startIcon={<CloseIcon />}
          >
            Close
          </SecondaryButton>
          <Button
            href={imageUrl}
            target="_blank"
            rel="noopener noreferrer"
            variant="contained"
            startIcon={<VisibilityIcon />}
            sx={{
              borderRadius: 8,
              padding: '10px 24px',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              boxShadow: '0 4px 12px rgba(41, 128, 185, 0.2)',
              background: 'linear-gradient(45deg, #2980b9 0%, #3498db 100%)',
              color: '#fff',
              '&:hover': {
                background: 'linear-gradient(45deg, #3498db 0%, #5dade2 100%)',
                boxShadow: '0 6px 16px rgba(41, 128, 185, 0.3)',
              }
            }}
          >
            Open Original
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Improved image handling for leads and orders tables
const TableCatalogueImage: React.FC<{ lead: Lead }> = ({ lead }) => {
  // If there's a design reference, use that
  if (lead.design_reference) {
    return <ImagePreview src={lead.design_reference} />;
  }
  
  // Check for catalogue selection
  if (lead.catalogue_selection && lead.catalogue_selection.length > 0) {
    return <ImagePreview src={lead.catalogue_selection[0]} />;
  }
  
  // Fallback: empty image placeholder
  return <ImagePreview src={null} />;
};

// Add CategoryChips component for jewelry categories with updated styling
const CategoryChips: React.FC<{ categories: string | string[] }> = ({ categories }) => {
  if (!categories || (Array.isArray(categories) && categories.length === 0)) return null;

  const categoryList = Array.isArray(categories)
    ? categories
    : categories.split(',').map(cat => cat.trim()).filter(Boolean);

  return (
    <Stack direction="row" spacing={0.5} flexWrap="wrap" sx={{ gap: '4px' }}>
      {categoryList.map((category, index) => (
        <Chip
          key={index}
          label={category}
          size="medium"
          sx={{
            height: 'auto',
            py: 0.8,
            px: 1,
            fontSize: '0.95rem',
            backgroundColor: 'rgba(41, 128, 185, 0.1)',
            color: '#2980b9',
            fontWeight: 500,
            borderRadius: '16px',
            fontFamily: '"Poppins", "Roboto", sans-serif',
            border: '1px solid rgba(41, 128, 185, 0.2)',
            '& .MuiChip-label': {
              padding: '2px 8px',
            },
          }}
        />
      ))}
    </Stack>
  );
};

// Add IconText component for table headers
interface IconTextProps {
  icon: React.ReactNode;
  text: string;
}

const IconText: React.FC<IconTextProps> = ({ icon, text }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    {icon}
    <Typography variant="body2" fontWeight={600} fontSize="1.1rem" fontFamily='"Poppins", "Roboto", sans-serif'>
      {text}
    </Typography>
  </Box>
);

// StyledTextField component
const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: 'rgba(41, 128, 185, 0.3)',
    },
    '&:hover fieldset': {
      borderColor: 'rgba(41, 128, 185, 0.5)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#3498db',
    },
  },
  '& .MuiInputLabel-root': {
    fontFamily: '"Poppins", "Roboto", sans-serif',
    fontSize: '1rem',
    '&.Mui-focused': {
      color: '#2980b9',
    },
  },
  '& .MuiInputBase-input': {
    fontFamily: '"Poppins", "Roboto", sans-serif',
    fontSize: '1rem',
  },
}));

// Status chip component
interface StatusChipProps {
  statuscolor: string;
}

const StatusChip = styled(Chip, {
  shouldForwardProp: (prop) => prop !== 'statuscolor'
})<StatusChipProps>(({ statuscolor }) => ({
  fontSize: '0.95rem',
  fontWeight: 500,
  fontFamily: '"Poppins", "Roboto", sans-serif',
  backgroundColor: `rgba(${statuscolor}, 0.1)`,
  color: `rgb(${statuscolor})`,
  border: `1px solid rgba(${statuscolor}, 0.3)`,
  borderRadius: 16,
  height: 32,
  padding: '0 6px',
}));

// Main component
const MarketingPage: React.FC = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [orders, setOrders] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    occasion: '',
    store: '',
    jewelry_type: '',
    minBudget: '',
    maxBudget: ''
  });
  const [tabValue, setTabValue] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchLeads();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchLeads = async () => {
    setLoading(true);
    const params: Record<string, string> = {};

    // Add filters
    if (filters.status) {
      params.status = filters.status;
    }
    if (filters.occasion) {
      params.occasion = filters.occasion;
    }
    
    // Don't apply store/jewelry_type at API level - we'll filter client-side
    // for better control

    // Set budget parameters if they exist
    if (filters.minBudget) {
      params.min_budget = filters.minBudget;
    }
    if (filters.maxBudget) {
      params.max_budget = filters.maxBudget;
    }

    // Request all records
    params.limit = '100';
    params.page_size = '100';

    try {
      console.log("Fetching leads with params:", params);
      const data = await getLeads(params);
      console.log("API response structure:", Object.keys(data));
      
      // Check if we have a paginated response (with results property)
      // or a direct array of leads
      let apiLeads = Array.isArray(data) ? data : data.results;
      
      console.log(`Received ${apiLeads?.length || 0} leads from API`);
      
      // Apply client-side filtering for store and jewelry_type
      let filteredLeads = apiLeads;
      
      // Filter by store if needed
      if (filters.store) {
        console.log(`Filtering by store: ${filters.store}`);
        const storeValues = filteredLeads.map((lead: Lead) => lead.store);
        console.log("All store values in data:", storeValues);
        
        filteredLeads = filteredLeads.filter((lead: Lead) => {
          // Try exact match
          const exactMatch = lead.store === filters.store;
          
          // Try lowercase match
          const lowercaseMatch = lead.store?.toLowerCase() === filters.store.toLowerCase();
          
          // Try includes match
          const includesMatch = lead.store?.includes(filters.store) || filters.store.includes(lead.store || '');
          
          console.log(`Store filter check for ${lead.store}: exact=${exactMatch}, lowercase=${lowercaseMatch}, includes=${includesMatch}`);
          
          return exactMatch || lowercaseMatch || includesMatch;
        });
      }
      
      // Filter by jewelry type if needed
      if (filters.jewelry_type) {
        console.log(`Filtering by jewelry type: ${filters.jewelry_type}`);
        
        // Debug: Log all jewelry categories
        const jewelryTypes = filteredLeads.map((lead: Lead) => lead.jewelry_categories);
        console.log("All jewelry types in data:", jewelryTypes);
        
        filteredLeads = filteredLeads.filter((lead: Lead) => {
          // Try multiple matching methods
          const exactMatch = lead.jewelry_categories === filters.jewelry_type;
          const lowercaseMatch = lead.jewelry_categories?.toLowerCase() === filters.jewelry_type.toLowerCase();
          const includesMatch = 
            lead.jewelry_categories?.includes(filters.jewelry_type) || 
            filters.jewelry_type.includes(lead.jewelry_categories || '');
          
          console.log(`Jewelry filter check for ${lead.jewelry_categories}: exact=${exactMatch}, lowercase=${lowercaseMatch}, includes=${includesMatch}`);
          
          return exactMatch || lowercaseMatch || includesMatch;
        });
      }
      
      console.log(`After filtering: ${filteredLeads.length} leads remain`);
      setLeads(filteredLeads);
    } catch (error) {
      console.error('Error fetching leads:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const applyFilters = () => {
    fetchLeads();
  };

  const resetFilters = () => {
    setFilters({
      status: '',
      occasion: '',
      store: '',
      jewelry_type: '',
      minBudget: '',
      maxBudget: ''
    });
    // After resetting filters, immediately fetch all leads
    fetchLeads();
  };

  const handleStatusChange = async (leadId: number, newStatus: string, lead: Lead) => {
    try {
      // If changing to order status, open confirmation dialog
      if (newStatus === 'order') {
        setSelectedLead(lead);
        setDialogOpen(true);
        return;
      }

      await updateLeadStatus(leadId, newStatus);
      fetchLeads(); // Refresh the list
    } catch (error: any) {
      console.error('Error updating lead status:', error);
    }
  };

  const handleCreateOrder = async () => {
    if (!selectedLead) return;

    try {
      // Just update the lead status to order
      // This is all that's needed from the marketing perspective
      await updateLeadStatus(selectedLead.id, 'order');

      // Note: We're not creating an order review record here anymore
      // That will be handled by the production team

      setSuccessMessage("Lead has been converted to an order successfully!");
      setDialogOpen(false);
      fetchLeads(); // Refresh both leads and orders lists

      // Switch to Orders tab
      setTabValue(1);
    } catch (error: any) {
      console.error('Error updating lead status:', error);
      setSuccessMessage(null);
      // Show error message to user
      alert(`Error converting lead to order: ${error.message || 'Unknown error'}`);
    }
  };

  // Determine segmentation tags based on lead properties
  const getSegmentationTags = (lead: Lead): SegmentationTag[] => {
    const tags: SegmentationTag[] = [];

    // Price sensitivity
    if (lead.budget < 25000) {
      tags.push({ label: 'Budget Conscious', color: 'warning' });
    } else if (lead.budget > 100000) {
      tags.push({ label: 'Premium Buyer', color: 'success' });
    }

    // Urgency - assuming timeline is a date string
    const timelineDate = new Date(lead.timeline);
    const now = new Date();
    const daysDifference = Math.floor((timelineDate.getTime() - now.getTime()) / (1000 * 3600 * 24));

    if (daysDifference < 30) {
      tags.push({ label: 'Urgent', color: 'error' });
    } else if (daysDifference < 90) {
      tags.push({ label: 'Medium Term', color: 'warning' });
    } else {
      tags.push({ label: 'Long Term', color: 'info' });
    }

    // Occasion type
    if (lead.occasion === 'wedding') {
      tags.push({ label: 'Wedding', color: 'success' });
    } else if (lead.occasion === 'special_occasions') {
      tags.push({ label: 'Special Occasion', color: 'info' });
    } else if (lead.occasion === 'casual') {
      tags.push({ label: 'Casual', color: 'default' });
    }

    return tags;
  };

  // Calculate funnel data
  const calculateFunnelData = (): FunnelDataItem[] => {
    const statusCounts: Record<string, number> = {};
    leads.forEach((lead: Lead) => {
      // Special handling for 'new' status to display as 'Just In'
      let status = lead.status === 'new' ? 'Just In' : lead.status.charAt(0).toUpperCase() + lead.status.slice(1);
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    const total = leads.length;

    const statusColors: Record<string, string> = {
      'Just In': '#3498db',    // Blue
      Contacted: '#5dade2',   // Light Blue
      Warm: '#e67e22',        // Orange
      Converted: '#2ecc71',   // Green
      Dropped: '#e74c3c',     // Red
      Order: '#9b59b6'        // Purple
    };

    const funnelSteps = ['Just In', 'Contacted', 'Warm', 'Converted', 'Order'];

    return funnelSteps.map(status => ({
      status,
      count: statusCounts[status] || 0,
      color: statusColors[status],
      percentage: total > 0 ? ((statusCounts[status] || 0) / total) * 100 : 0
    }));
  };

  // Calculate insights data
  const calculateInsightsData = (): InsightsData => {
    const occasionCounts: Record<string, number> = {};
    let totalBudget = 0;
    let premiumLeads = 0;

    leads.forEach((lead: Lead) => {
      occasionCounts[lead.occasion] = (occasionCounts[lead.occasion] || 0) + 1;
      totalBudget += lead.budget;

      if (lead.budget > 100000) {
        premiumLeads++;
      }
    });

    const convertedLeads = leads.filter(lead => lead.status === 'converted' || lead.status === 'order').length;
    const conversionRate = leads.length > 0 ? (convertedLeads / leads.length) * 100 : 0;

    return {
      occasionBreakdown: occasionCounts,
      totalBudget,
      leadCount: leads.length,
      premiumLeads,
      conversionRate,
      monthlyGrowth: 12.5 // Mocked value, would come from real data in production
    };
  };

  const funnelData = calculateFunnelData();
  const insightsData = calculateInsightsData();

  // Get occasion icon based on occasion type
  const getOccasionIcon = (occasion: string) => {
    switch (occasion) {
      case 'wedding':
        return <FavoriteIcon sx={{ fontSize: '1.4rem', color: '#e74c3c' }} />;
      case 'birthday':
        return <CakeIcon sx={{ fontSize: '1.4rem', color: '#3498db' }} />;
      case 'engagement':
        return <DiamondIcon sx={{ fontSize: '1.4rem', color: '#9b59b6' }} />;
      case 'festival':
        return <CelebrationIcon sx={{ fontSize: '1.4rem', color: '#f39c12' }} />;
      case 'daily_wear':
        return <LocalMallIcon sx={{ fontSize: '1.4rem', color: '#27ae60' }} />;
      default:
        return <GridViewIcon sx={{ fontSize: '1.4rem', color: '#7f8c8d' }} />;
    }
  };

  // Get status color code for chips
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getStatusColorCode = (status: string): string => {
    switch (status) {
      case 'new': return '52, 152, 219';      // Blue (Just In)
      case 'contacted': return '155, 89, 182'; // Purple
      case 'warm': return '230, 126, 34';      // Orange
      case 'converted': return '46, 204, 113'; // Green
      case 'dropped': return '231, 76, 60';    // Red
      case 'order': return '52, 152, 219';     // Blue
      default: return '52, 73, 94';            // Dark
    }
  };

  // Update store filter dropdown options to match exactly what's in the database
  const storeOptions = [
    { value: "", label: "All" },
    { value: "khan_market", label: "Khan Market" },
    { value: "amrawatta", label: "Amrawatta" }
  ];

  // Update jewelry type filter dropdown options to match exactly what's in the database
  const jewelryTypeOptions = [
    { value: "", label: "All" },
    { value: "necklace", label: "Necklace" },
    { value: "earrings", label: "Earrings" },
    { value: "bracelet", label: "Bracelet" },
    { value: "bangle", label: "Bangle" },
    { value: "brooch", label: "Brooch" },
    { value: "ring", label: "Ring" },
    { value: "pendant", label: "Pendant" },
    { value: "bridal_collection", label: "Bridal Collection" }
  ];

  // Add this helper function to help diagnose filter issues
  const debugLead = (lead: Lead) => {
    console.log('Lead debug:');
    console.log('- ID:', lead.id);
    console.log('- Name:', lead.name);
    console.log('- Store:', lead.store);
    console.log('- Jewelry categories:', lead.jewelry_categories);
    console.log('- Status:', lead.status);
  };

  return (
    <Box sx={{
      fontFamily: '"Poppins", "Roboto", sans-serif',
      p: { xs: 1, sm: 2, md: 3 },
      background: 'linear-gradient(to bottom, rgba(249, 249, 246, 0.7), rgba(255, 255, 255, 1))',
      overflowX: 'auto'
    }}>
      <PageTitle variant="h4">
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <DiamondIcon sx={{ fontSize: '2.3rem', color: '#3498db' }} />
          Marketing Dashboard
        </Box>
      </PageTitle>

      {/* Dashboard Overview */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
        <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', md: '250px' } }}>
          <InsightsCard>
            <InsightIcon bgcolor="#3498db">
              <TrendingUpIcon sx={{ fontSize: '1.8rem' }} />
            </InsightIcon>
            <InsightLabel>Total Budget</InsightLabel>
            <InsightValue>₹{insightsData.totalBudget.toLocaleString()}</InsightValue>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUpIcon sx={{ color: '#27ae60', fontSize: '1.1rem', mr: 0.5 }} />
              <Typography variant="caption" sx={{ color: '#27ae60', fontWeight: 500, fontSize: '1rem' }}>
                12.3% growth
              </Typography>
            </Box>
          </InsightsCard>
        </Box>

        <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', md: '250px' } }}>
          <InsightsCard>
            <InsightIcon bgcolor="#e74c3c">
              <DiamondIcon sx={{ fontSize: '1.8rem' }} />
            </InsightIcon>
            <InsightLabel>Premium Leads</InsightLabel>
            <InsightValue>{insightsData.premiumLeads}</InsightValue>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Typography variant="caption" sx={{ color: '#666', fontWeight: 500, fontSize: '1rem' }}>
                {leads.length > 0 ? Math.round((insightsData.premiumLeads / leads.length) * 100) : 0}% of total leads
              </Typography>
            </Box>
          </InsightsCard>
        </Box>

        <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', md: '250px' } }}>
          <InsightsCard>
            <InsightIcon bgcolor="#2ecc71">
              <CheckCircleIcon sx={{ fontSize: '1.8rem' }} />
            </InsightIcon>
            <InsightLabel>Conversion Rate</InsightLabel>
            <InsightValue>{insightsData.conversionRate.toFixed(1)}%</InsightValue>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Typography variant="caption" sx={{ color: '#666', fontWeight: 500, fontSize: '1rem' }}>
                {Math.round(insightsData.monthlyGrowth)}% monthly increase
              </Typography>
            </Box>
          </InsightsCard>
        </Box>

        <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', md: '250px' } }}>
          <InsightsCard>
            <InsightIcon bgcolor="#3498db">
              <StoreIcon sx={{ fontSize: '1.8rem' }} />
            </InsightIcon>
            <InsightLabel>Total Leads</InsightLabel>
            <InsightValue>{leads.length + orders.length}</InsightValue>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Typography variant="caption" sx={{ color: '#666', fontWeight: 500, fontSize: '1rem' }}>
                {orders.length} converted to orders
              </Typography>
            </Box>
          </InsightsCard>
        </Box>
      </Box>

      {/* Sales Funnel & Occasion Breakdown */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
        <Box sx={{ flex: '3 1 550px', minWidth: { xs: '100%', lg: '550px' } }}>
          <InsightsCard>
            <InsightTitle>Occasion Breakdown</InsightTitle>
            <Box sx={{ mb: 3 }}>
              {Object.entries(insightsData.occasionBreakdown).map(([occasion, count], index) => (
                <Box key={occasion} sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 2,
                  mb: 1,
                  borderRadius: 2,
                  backgroundColor: index % 2 === 0 ? 'rgba(249, 249, 246, 0.6)' : 'rgba(249, 249, 246, 0.3)',
                  border: '1px solid rgba(41, 128, 185, 0.1)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(41, 128, 185, 0.05)',
                    transform: 'translateX(5px)'
                  }
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getOccasionIcon(occasion)}
                    <Typography sx={{
                      fontSize: '1.2rem',
                      color: '#2c3e50',
                      textTransform: 'capitalize',
                      fontFamily: '"Poppins", "Roboto", sans-serif',
                      fontWeight: 500,
                    }}>
                      {occasion.replace('_', ' ')}
                    </Typography>
                  </Box>
                  <Box sx={{
                    px: 2,
                    py: 0.8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(41, 128, 185, 0.1)',
                    border: '1px solid rgba(41, 128, 185, 0.2)'
                  }}>
                    <Typography sx={{
                      fontSize: '1.1rem',
                      color: '#2980b9',
                      fontWeight: 600,
                      fontFamily: '"Poppins", "Roboto", sans-serif',
                    }}>
                      {count} {count === 1 ? 'lead' : 'leads'}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </InsightsCard>
        </Box>

        <Box sx={{ flex: '2 1 300px', minWidth: { xs: '100%', md: '300px' } }}>
          <InsightsCard>
            <InsightTitle>Sales Funnel Analysis</InsightTitle>
            <Typography sx={{ mb: 3, color: '#666666', fontSize: '1.1rem', fontFamily: '"Poppins", "Roboto", sans-serif' }}>
              Lead progression from initial contact to order
            </Typography>

            {funnelData.map((item, index) => (
              <Box key={item.status} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.8, alignItems: 'center' }}>
                  <Typography
                    component="div" 
                    sx={{
                      color: item.color,
                      fontWeight: 600,
                      fontFamily: '"Poppins", "Roboto", sans-serif',
                      fontSize: '1.1rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5
                    }}
                  >
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: item.color
                      }}
                    />
                    {item.status}
                  </Typography>
                  <Typography
                    sx={{
                      color: '#666',
                      fontSize: '1rem',
                      fontFamily: '"Poppins", "Roboto", sans-serif',
                    }}
                  >
                    {item.count} ({item.percentage.toFixed(1)}%)
                  </Typography>
                </Box>
                <Box sx={{
                  width: '100%',
                  height: 22,
                  backgroundColor: `${item.color}20`,
                  borderRadius: 4,
                  position: 'relative',
                  mb: 2,
                  overflow: 'hidden',
                  border: `1px solid ${item.color}40`
                }}>
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    height: '100%',
                    width: `${Math.max(item.percentage, 3)}%`,
                    backgroundColor: item.color,
                    borderRadius: 4,
                    transition: 'width 0.5s ease-in-out'
                  }} />
                </Box>
              </Box>
            ))}
          </InsightsCard>
        </Box>
      </Box>

      {/* Tabs for Leads vs Orders */}
      <Box sx={{
        borderBottom: 1,
        borderColor: 'rgba(41, 128, 185, 0.2)',
        mb: 3,
        '& button': {
          fontFamily: '"Poppins", "Roboto", sans-serif',
          fontWeight: 600,
          fontSize: '1.2rem',
          color: '#666666',
          '&.Mui-selected': {
            color: '#2980b9',
          }
        },
        '& .MuiTabs-indicator': {
          backgroundColor: '#3498db',
          height: 3
        }
      }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="leads and orders tabs">
          <Tab label={`Leads (${leads.length})`} />
          <Tab label={`Orders (${orders.length})`} />
        </Tabs>
      </Box>

      {/* Leads Tab */}
      <TabPanel value={tabValue} index={0}>
        <FilterContainer>
          <FilterTitle variant="h6">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <FilterListIcon sx={{ color: '#3498db' }} />
              Filter Leads
            </Box>
          </FilterTitle>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Status</InputLabel>
                <Select
                  name="status"
                  value={filters.status}
                  label="Status"
                  onChange={(event) => handleSelectChange(event as SelectChangeEvent<string>)}
                  sx={{
                    textTransform: 'capitalize',
                    fontSize: '1rem',
                    fontFamily: '"Poppins", "Roboto", sans-serif',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.3)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3498db',
                    }
                  }}
                >
                  <MenuItem value="" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>All</MenuItem>
                  <MenuItem value="new" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Just In</MenuItem>
                  <MenuItem value="contacted" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Contacted</MenuItem>
                  <MenuItem value="warm" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Warm</MenuItem>
                  <MenuItem value="converted" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Converted</MenuItem>
                  <MenuItem value="dropped" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Dropped</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Occasion</InputLabel>
                <Select
                  name="occasion"
                  value={filters.occasion}
                  label="Occasion"
                  onChange={(event) => handleSelectChange(event as SelectChangeEvent<string>)}
                  sx={{
                    textTransform: 'capitalize',
                    fontSize: '1rem',
                    fontFamily: '"Poppins", "Roboto", sans-serif',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.3)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3498db',
                    }
                  }}
                >
                  <MenuItem value="" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>All</MenuItem>
                  <MenuItem value="wedding" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Wedding</MenuItem>
                  <MenuItem value="special_occasions" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Special occasions</MenuItem>
                  <MenuItem value="casual" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Casual</MenuItem>
                  <MenuItem value="other" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Other</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Store</InputLabel>
                <Select
                  name="store"
                  value={filters.store}
                  label="Store"
                  onChange={(event) => handleSelectChange(event as SelectChangeEvent<string>)}
                  sx={{
                    textTransform: 'capitalize',
                    fontSize: '1rem',
                    fontFamily: '"Poppins", "Roboto", sans-serif',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.3)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3498db',
                    }
                  }}
                >
                  {storeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value} sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Jewelry Type</InputLabel>
                <Select
                  name="jewelry_type"
                  value={filters.jewelry_type}
                  label="Jewelry Type"
                  onChange={(event) => handleSelectChange(event as SelectChangeEvent<string>)}
                  sx={{
                    textTransform: 'capitalize',
                    fontSize: '1rem',
                    fontFamily: '"Poppins", "Roboto", sans-serif',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.3)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(41, 128, 185, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3498db',
                    }
                  }}
                >
                  {jewelryTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value} sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <StyledTextField
                name="minBudget"
                label="Min Budget"
                type="number"
                value={filters.minBudget}
                onChange={handleInputChange}
                fullWidth
                size="small"
                InputProps={{
                  style: { fontSize: '1rem' }
                }}
              />
            </Box>

            <Box sx={{ flex: '1 1 200px', minWidth: { xs: '100%', sm: '200px' } }}>
              <StyledTextField
                name="maxBudget"
                label="Max Budget"
                type="number"
                value={filters.maxBudget}
                onChange={handleInputChange}
                fullWidth
                size="small"
                InputProps={{
                  style: { fontSize: '1rem' }
                }}
              />
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <ActionButton
              onClick={applyFilters}
              startIcon={<SearchIcon />}
            >
              Apply Filters
            </ActionButton>
            <SecondaryButton
              onClick={resetFilters}
              startIcon={<ReplayIcon />}
            >
              Reset
            </SecondaryButton>
          </Box>
        </FilterContainer>

        <StyledTableContainer>
          <Table size="medium" aria-label="leads table" sx={{ minWidth: 1200 }}>
            <StyledTableHead>
              <TableRow>
                <TableCell><IconText icon={<AccountCircleIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Name" /></TableCell>
                <TableCell><IconText icon={<StoreIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Store" /></TableCell>
                <TableCell><IconText icon={<MonetizationOnIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Budget" /></TableCell>
                <TableCell><IconText icon={<ImageIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Design" /></TableCell>
                <TableCell width="15%"><IconText icon={<CategoryIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Jewelry Type" /></TableCell>
                <TableCell><IconText icon={<CalendarTodayIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Timeline" /></TableCell>
                <TableCell><IconText icon={<CalendarTodayIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Created" /></TableCell>
                <TableCell><IconText icon={<TrendingUpIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Status" /></TableCell>
                <TableCell width="15%" sx={{ minWidth: '150px' }}><IconText icon={<DiamondIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Segment" /></TableCell>
                <TableCell width="150px" sx={{ minWidth: '150px' }}><IconText icon={<CheckCircleIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Actions" /></TableCell>
              </TableRow>
            </StyledTableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                      <CircularProgress size={40} sx={{ color: "#3498db" }} />
                    </Box>
                  </TableCell>
                </TableRow>
              ) : leads.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align="center" sx={{
                    fontSize: '1.2rem',
                    py: 4,
                    color: '#2980b9',
                    fontFamily: '"Poppins", "Roboto", sans-serif'
                  }}>
                    No leads found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                leads.map((lead: Lead) => (
                  <StyledTableRow key={lead.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AccountCircleIcon sx={{ color: '#3498db', fontSize: '1.4rem' }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600} fontSize="1.1rem" sx={{
                            color: '#2c3e50',
                            fontFamily: '"Poppins", "Roboto", sans-serif'
                          }}>
                            {lead.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <PhoneIcon sx={{ fontSize: '1rem', color: '#3498db' }} />
                            <Typography variant="caption" color="#666666" fontSize="1rem" fontFamily='"Poppins", "Roboto", sans-serif'>
                              {lead.phone}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{
                        color: '#2c3e50',
                        fontFamily: '"Poppins", "Roboto", sans-serif'
                      }}>
                        {lead.store === 'khan_market' ? 'Khan Market' :
                         lead.store === 'amrawatta' ? 'Amrawatta' : '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} color="#3498db" fontSize="1.1rem" fontFamily='"Poppins", "Roboto", sans-serif'>
                        ₹{lead.budget.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <TableCatalogueImage lead={lead} />
                    </TableCell>
                    <TableCell width="15%">
                      <CategoryChips categories={lead.jewelry_categories} />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{
                        color: '#2c3e50',
                        fontFamily: '"Poppins", "Roboto", sans-serif',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                      }}>
                        <CalendarTodayIcon sx={{ fontSize: '1rem', color: '#3498db' }} />
                        {new Date(lead.timeline).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{
                        color: '#666666',
                        fontFamily: '"Poppins", "Roboto", sans-serif'
                      }}>
                        {new Date(lead.created_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <FormControl size="small" fullWidth>
                        <Select
                          value={lead.status}
                          onChange={(event) => {
                            const newStatus = event.target.value as string;
                            handleStatusChange(lead.id, newStatus, lead);
                          }}
                          sx={{
                            textTransform: 'capitalize',
                            fontSize: '1rem',
                            fontFamily: '"Poppins", "Roboto", sans-serif',
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: 'rgba(41, 128, 185, 0.3)',
                            },
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              borderColor: 'rgba(41, 128, 185, 0.5)',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#3498db',
                            },
                            '& .MuiSelect-select': {
                              fontSize: '1rem',
                              fontFamily: '"Poppins", "Roboto", sans-serif',
                              padding: '8px 14px'
                            }
                          }}
                        >
                          <MenuItem value="new" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Just In</MenuItem>
                          <MenuItem value="contacted" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Contacted</MenuItem>
                          <MenuItem value="warm" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Warm</MenuItem>
                          <MenuItem value="converted" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Converted</MenuItem>
                          <MenuItem value="dropped" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Dropped</MenuItem>
                          <MenuItem value="order" sx={{ fontFamily: '"Poppins", "Roboto", sans-serif', fontSize: '1rem' }}>Order</MenuItem>
                        </Select>
                      </FormControl>
                    </TableCell>
                    <TableCell width="15%" sx={{ minWidth: '150px' }}>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8, minHeight: '32px' }}>
                        {getSegmentationTags(lead).map((tag, idx) => (
                          <StatusChip
                            key={idx}
                            label={tag.label}
                            size="medium"
                            statuscolor={
                              tag.color === 'success' ? '46, 204, 113' :
                              tag.color === 'warning' ? '230, 126, 34' :
                              tag.color === 'error' ? '231, 76, 60' :
                              tag.color === 'info' ? '52, 152, 219' : '127, 140, 141'
                            }
                          />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell width="150px" sx={{ minWidth: '150px' }}>
                      <ActionButton
                        size="medium"
                        startIcon={<AddAlertIcon />}
                        sx={{
                          fontSize: '1rem',
                          px: 2,
                          py: 1,
                          minWidth: '140px',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        Follow-up
                      </ActionButton>
                    </TableCell>
                  </StyledTableRow>
                ))
              )}
            </TableBody>
          </Table>
        </StyledTableContainer>
      </TabPanel>

      {/* Orders Tab */}
      <TabPanel value={tabValue} index={1}>
        <StyledTableContainer>
          <Table size="medium" aria-label="orders table" sx={{ minWidth: 1100 }}>
            <StyledTableHead>
              <TableRow>
                <TableCell><IconText icon={<AccountCircleIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Order ID" /></TableCell>
                <TableCell><IconText icon={<AccountCircleIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Customer" /></TableCell>
                <TableCell><IconText icon={<StoreIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Store" /></TableCell>
                <TableCell><IconText icon={<MonetizationOnIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Budget" /></TableCell>
                <TableCell><IconText icon={<ImageIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Design" /></TableCell>
                <TableCell width="15%"><IconText icon={<CategoryIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Jewelry Type" /></TableCell>
                <TableCell><IconText icon={<CalendarTodayIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Timeline" /></TableCell>
                <TableCell><IconText icon={<CalendarTodayIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Created" /></TableCell>
                <TableCell width="150px"><IconText icon={<VisibilityIcon fontSize="small" sx={{ color: '#3498db' }} />} text="Actions" /></TableCell>
              </TableRow>
            </StyledTableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                      <CircularProgress size={40} sx={{ color: "#3498db" }} />
                    </Box>
                  </TableCell>
                </TableRow>
              ) : orders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{
                    fontSize: '1.2rem',
                    py: 4,
                    color: '#2980b9',
                    fontFamily: '"Poppins", "Roboto", sans-serif'
                  }}>
                    No orders found
                  </TableCell>
                </TableRow>
              ) : (
                orders.map((order: Lead) => (
                  <StyledTableRow key={order.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} fontSize="1.1rem" color="#3498db" fontFamily='"Poppins", "Roboto", sans-serif'>
                        #{order.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AccountCircleIcon sx={{ color: '#3498db', fontSize: '1.4rem' }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600} fontSize="1.1rem" sx={{ color: '#2c3e50', fontFamily: '"Poppins", "Roboto", sans-serif' }}>
                            {order.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <PhoneIcon sx={{ fontSize: '1rem', color: '#3498db' }} />
                            <Typography variant="caption" color="#666666" fontSize="1rem" fontFamily='"Poppins", "Roboto", sans-serif'>
                              {order.phone}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{ color: '#2c3e50', fontFamily: '"Poppins", "Roboto", sans-serif' }}>
                        {order.store === 'khan_market' ? 'Khan Market' :
                         order.store === 'amrawatta' ? 'Amrawatta' : '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} color="#3498db" fontSize="1.1rem" fontFamily='"Poppins", "Roboto", sans-serif'>
                        ₹{order.budget.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <TableCatalogueImage lead={order} />
                    </TableCell>
                    <TableCell>
                      <CategoryChips categories={order.jewelry_categories} />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{
                        color: '#2c3e50',
                        fontFamily: '"Poppins", "Roboto", sans-serif',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                      }}>
                        <CalendarTodayIcon sx={{ fontSize: '1rem', color: '#3498db' }} />
                        {new Date(order.timeline).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontSize="1.1rem" sx={{ color: '#666666', fontFamily: '"Poppins", "Roboto", sans-serif' }}>
                        {new Date(order.created_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell width="150px">
                      <ActionButton
                        size="medium"
                        startIcon={<VisibilityIcon />}
                        sx={{
                          fontSize: '1rem',
                          px: 2,
                          py: 1,
                          minWidth: '140px',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        View Details
                      </ActionButton>
                    </TableCell>
                  </StyledTableRow>
                ))
              )}
            </TableBody>
          </Table>
        </StyledTableContainer>
      </TabPanel>

      {/* Order Creation Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: 12,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          fontFamily: '"Poppins", "Roboto", sans-serif',
          color: '#2980b9',
          fontSize: '1.5rem',
          fontWeight: 600,
          borderBottom: '1px solid rgba(41, 128, 185, 0.2)',
          pb: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 1.5,
          backgroundColor: 'rgba(249, 249, 246, 0.6)'
        }}>
          <DiamondIcon sx={{ color: '#3498db' }} />
          Convert Lead to Order
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 1 }}>
          <Typography variant="body1" fontSize="1.1rem" sx={{ mb: 3, color: '#2c3e50', fontFamily: '"Poppins", "Roboto", sans-serif' }}>
            Are you sure you want to convert this lead to an order? This will mark the lead as ready for review by the production department.
          </Typography>
          {selectedLead && (
            <Box sx={{
              mt: 2,
              p: 3,
              borderRadius: 3,
              border: '1px solid rgba(41, 128, 185, 0.2)',
              backgroundColor: 'rgba(249, 249, 246, 0.6)'
            }}>
              <Typography variant="subtitle1" fontSize="1.1rem" sx={{
                color: '#2c3e50',
                mb: 1,
                fontFamily: '"Poppins", "Roboto", sans-serif',
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <AccountCircleIcon sx={{ color: '#3498db', fontSize: '1.4rem' }} />
                Customer: <Box component="span" fontWeight={600} color="#2c3e50" ml={0.5}>{selectedLead.name}</Box>
              </Typography>
              <Typography variant="subtitle1" fontSize="1.1rem" sx={{
                color: '#2c3e50',
                mb: 1,
                fontFamily: '"Poppins", "Roboto", sans-serif',
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <MonetizationOnIcon sx={{ color: '#3498db', fontSize: '1.4rem' }} />
                Budget: <Box component="span" fontWeight={600} color="#3498db" ml={0.5}>₹{selectedLead.budget.toLocaleString()}</Box>
              </Typography>
              <Typography variant="subtitle1" fontSize="1.1rem" sx={{
                color: '#2c3e50',
                fontFamily: '"Poppins", "Roboto", sans-serif',
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <CalendarTodayIcon sx={{ color: '#3498db', fontSize: '1.4rem' }} />
                Timeline: <Box component="span" fontWeight={600} color="#2c3e50" ml={0.5}>{new Date(selectedLead.timeline).toLocaleDateString()}</Box>
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(41, 128, 185, 0.2)', backgroundColor: 'rgba(249, 249, 246, 0.6)' }}>
          <SecondaryButton
            onClick={() => setDialogOpen(false)}
            startIcon={<CloseIcon />}
            sx={{ fontSize: '1rem' }}
          >
            Cancel
          </SecondaryButton>
          <ActionButton
            onClick={handleCreateOrder}
            startIcon={<CheckCircleIcon />}
            sx={{ fontSize: '1rem' }}
          >
            Create Order
          </ActionButton>
        </DialogActions>
      </Dialog>

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSuccessMessage(null)}
          severity="success"
          sx={{
            width: '100%',
            fontFamily: '"Poppins", "Roboto", sans-serif',
            backgroundColor: 'rgba(46, 204, 113, 0.9)',
            color: '#fff',
            fontWeight: 500,
            fontSize: '1.1rem',
            '& .MuiAlert-icon': {
              color: '#fff'
            }
          }}
        >
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MarketingPage;