import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Paper,
  Box,
  Container,
  Button,
  styled,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import useLeadFormStore from '../store/leadFormStore';
import { createLead } from '../api/api';
import JewelryBackground from '../components/layout/JewelryBackground';
import {
  StoreForm,
  NameForm,
  PhoneForm,
  AddressForm,
  BudgetForm,
  OccasionForm,
  TimelineForm,
  RemarksForm,
  DesignReferenceForm,
  JewelryCategoryForm,
  CatalogueSelectionForm,
  SuccessScreen
} from '../components/forms/LeadForm';
import FormWrapper from '../components/forms/FormWrapper';

// Import for the step icons
import StoreIcon from '@mui/icons-material/StoreMallDirectory';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import CelebrationIcon from '@mui/icons-material/Celebration';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import CommentIcon from '@mui/icons-material/Comment';
import ImageIcon from '@mui/icons-material/Image';
import CategoryIcon from '@mui/icons-material/Category';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

// Styled components
const PageContainer = styled(Container)`
  padding-top: 2rem;
  padding-bottom: 2rem;
  max-width: 900px;
`;

const StyledPaper = styled(Paper)`
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(174, 146, 45, 0.1);
  background: #ffffff;
  border: 1px solid rgba(201, 176, 55, 0.2);
`;

const FormHeader = styled(Box)`
  text-align: center;
  margin-bottom: 2.5rem;
`;

const FormTitle = styled(Typography)`
  font-family: 'Playfair Display', serif;
  color: #c9b037;
  font-weight: 600;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
`;

const FormSubtitle = styled(Typography)`
  font-family: 'Cormorant Garamond', serif;
  color: #8a7534;
  font-size: 1.2rem;
  margin-bottom: 2rem;
`;

const FormContent = styled(Box)`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const SectionDivider = styled(Divider)`
  margin: 1.5rem 0;
  background-color: rgba(201, 176, 55, 0.2);
`;

const SubmitButton = styled(Button)`
  background: linear-gradient(45deg, #8a7534 0%, #c9b037 100%);
  padding: 12px 24px;
  border-radius: 8px;
  text-transform: none;
  font-size: 1.1rem;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(201, 176, 55, 0.3);
  margin-top: 2rem;

  &:hover {
    background: linear-gradient(45deg, #c9b037 0%, #e6d56e 100%);
    box-shadow: 0 6px 16px rgba(201, 176, 55, 0.4);
  }
`;

const SalesPage: React.FC = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { formData, resetForm } = useLeadFormStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);
      setError(null);
      await createLead(formData);
      setShowSuccess(true);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (error: any) {
      console.error('Error submitting form:', error);
      setError(error.response?.data?.message || 'Failed to submit form. Please try again.');
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNewForm = () => {
    resetForm();
    setShowSuccess(false);
    setError(null);
  };

  // Dummy functions for form components
  const dummyNext = () => {};
  const dummyPrev = () => {};

  if (showSuccess) {
    return (
      <JewelryBackground>
        <PageContainer>
          <StyledPaper elevation={3}>
            <FormHeader>
              <FormTitle variant="h4">
                Symetree Jewelry
              </FormTitle>
              <FormSubtitle variant="subtitle1">
                Custom Order Form
              </FormSubtitle>
            </FormHeader>
            <SuccessScreen />
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleNewForm}
                sx={{
                  padding: '12px 24px',
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontSize: '1.1rem'
                }}
              >
                Create New Lead
              </Button>
            </Box>
          </StyledPaper>
        </PageContainer>
      </JewelryBackground>
    );
  }

  return (
    <JewelryBackground>
      <PageContainer>
        <StyledPaper elevation={3}>
          <FormHeader>
            <FormTitle variant="h4">
              Symetree Jewelry
            </FormTitle>
            <FormSubtitle variant="subtitle1">
              Custom Order Form
            </FormSubtitle>
          </FormHeader>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <FormContent>
              {/* Store Selection */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <StoreIcon /> Store Selection
                </Typography>
                <FormWrapper>
                  <StoreForm onNext={dummyNext} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Customer Information */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <PersonIcon /> Customer Information
                </Typography>
                <FormWrapper>
                  <NameForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Contact Information */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <PhoneIcon /> Contact Information
                </Typography>
                <FormWrapper>
                  <PhoneForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Address */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <HomeIcon /> Address
                </Typography>
                <FormWrapper>
                  <AddressForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Budget */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <AccountBalanceWalletIcon /> Budget
                </Typography>
                <FormWrapper>
                  <BudgetForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Occasion */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <CelebrationIcon /> Occasion
                </Typography>
                <FormWrapper>
                  <OccasionForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Timeline */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <CalendarTodayIcon /> Timeline
                </Typography>
                <FormWrapper>
                  <TimelineForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Design Reference */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <ImageIcon /> Design Reference
                </Typography>
                <FormWrapper>
                  <DesignReferenceForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Jewelry Category */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <CategoryIcon /> Jewelry Category
                </Typography>
                <FormWrapper>
                  <JewelryCategoryForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Catalogue Selection */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <ShoppingCartIcon /> Catalogue Selection
                </Typography>
                <FormWrapper>
                  <CatalogueSelectionForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <SectionDivider />

              {/* Remarks */}
              <Box>
                <Typography variant="h6" sx={{
                  fontFamily: '"Playfair Display", serif',
                  color: '#8a7534',
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <CommentIcon /> Remarks
                </Typography>
                <FormWrapper>
                  <RemarksForm onNext={dummyNext} onPrev={dummyPrev} />
                </FormWrapper>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <SubmitButton
                  type="submit"
                  disabled={isSubmitting ||
                    !formData.store ||
                    !formData.name ||
                    !formData.phone ||
                    !formData.address ||
                    !formData.budget ||
                    !formData.occasion ||
                    !formData.timeline
                  }
                >
                  {isSubmitting ?
                    <CircularProgress size={24} color="inherit" /> :
                    'Submit Lead'
                  }
                </SubmitButton>
                {(!formData.store ||
                  !formData.name ||
                  !formData.phone ||
                  !formData.address ||
                  !formData.budget ||
                  !formData.occasion ||
                  !formData.timeline) && (
                  <Typography variant="caption" color="error" sx={{ display: 'block', textAlign: 'center', mt: 1 }}>
                    Please fill in all required fields
                  </Typography>
                )}
              </Box>
            </FormContent>
          </form>
        </StyledPaper>
      </PageContainer>
    </JewelryBackground>
  );
};

export default SalesPage;