import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import Layout from './components/layout/Layout';
import useAuthStore from './store/authStore';

// Lazy load pages for better performance
const SalesPage = React.lazy(() => import('./pages/SalesPage'));
const MarketingPage = React.lazy(() => import('./pages/MarketingPage'));
const ProductionPage = React.lazy(() => import('./pages/ProductionPage'));
const LoginPage = React.lazy(() => import('./pages/LoginPage'));

// Create a custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#c9b037', // Gold color
      light: '#e6d56e',
      dark: '#9c8b28',
    },
    secondary: {
      main: '#7d5c27', // Bronze/copper tone
      light: '#a68952',
      dark: '#5e4415',
    },
    background: {
      default: '#faf9f6', // Ivory/off-white
      paper: '#fff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
  },
  typography: {
    fontFamily: '"Cormorant Garamond", "Playfair Display", "Roboto", serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 2,
          textTransform: 'none',
        },
        contained: {
          boxShadow: 'none',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
  },
});

interface ProtectedRouteProps {
  element: React.ReactNode;
  requiredRole?: string;
}

// Protected Route Component
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ element, requiredRole }) => {
  const { isAuthenticated, checkAuth, user } = useAuthStore();
  const location = useLocation();
  
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);
  
  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  // If role restriction is applied, check role
  if (requiredRole && user?.role !== requiredRole) {
    const redirectTo = 
      user?.role === 'sales' ? '/' :
      user?.role === 'marketing' ? '/marketing' :
      user?.role === 'production' ? '/production' : '/login';

    return <Navigate to={redirectTo} replace />;
  }
  
  // Otherwise render the protected component
  return <>{element}</>;
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <React.Suspense fallback={<div>Loading...</div>}>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            
            <Route path="/" element={
              <ProtectedRoute 
                element={
                  <Layout>
                    <SalesPage />
                  </Layout>
                } 
                requiredRole="sales"
              />
            } />
            
            <Route path="/marketing" element={
              <ProtectedRoute 
                element={
                  <Layout>
                    <MarketingPage />
                  </Layout>
                }
                requiredRole="marketing"
              />
            } />
            
            <Route path="/production" element={
              <ProtectedRoute 
                element={
                  <Layout>
                    <ProductionPage />
                  </Layout>
                }
                requiredRole="production"
              />
            } />
            
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </React.Suspense>
      </Router>
    </ThemeProvider>
  );
}

export default App;
