import { create } from 'zustand';

export type LeadFormData = {
  store: string;
  name: string;
  phone: string;
  address: string;
  budget: number | '';
  occasion: string;
  timeline: string;
  design_reference: string;
  design_file: File | null;
  remarks: string;
  catalogue_ids: number[];
  jewelry_categories: string[];
};

export type LeadFormState = {
  currentStep: number;
  totalSteps: number;
  formData: LeadFormData;
  isSubmitting: boolean;
  submitSuccess: boolean;
  submitError: string | null;
};

export type LeadFormActions = {
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  updateFormData: (data: Partial<LeadFormData>) => void;
  resetForm: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
  setSubmitSuccess: (success: boolean) => void;
  setSubmitError: (error: string | null) => void;
};

const initialFormData: LeadFormData = {
  store: '',
  name: '',
  phone: '',
  address: '',
  budget: '',
  occasion: '',
  timeline: '',
  design_reference: '',
  design_file: null,
  remarks: '',
  catalogue_ids: [],
  jewelry_categories: [],
};

const useLeadFormStore = create<LeadFormState & LeadFormActions>((set) => ({
  currentStep: 1,
  totalSteps: 12,
  formData: initialFormData,
  isSubmitting: false,
  submitSuccess: false,
  submitError: null,
  
  nextStep: () => set((state) => ({ 
    currentStep: Math.min(state.currentStep + 1, state.totalSteps) 
  })),
  
  prevStep: () => set((state) => ({ 
    currentStep: Math.max(state.currentStep - 1, 1) 
  })),
  
  goToStep: (step) => set(() => ({ 
    currentStep: step 
  })),
  
  updateFormData: (data) => set((state) => ({
    formData: { ...state.formData, ...data },
  })),
  
  resetForm: () => set(() => ({
    currentStep: 1,
    formData: initialFormData,
    isSubmitting: false,
    submitSuccess: false,
    submitError: null,
  })),
  
  setSubmitting: (isSubmitting) => set(() => ({ isSubmitting })),
  
  setSubmitSuccess: (success) => set(() => ({ submitSuccess: success })),
  
  setSubmitError: (error) => set(() => ({ submitError: error })),
}));

export default useLeadFormStore; 