import { create } from 'zustand';
import { login as apiLogin, getUserProfile } from '../api/api';

interface User {
  id: number;
  username: string;
  role: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
}

const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: !!localStorage.getItem('token'),
  user: null,
  token: localStorage.getItem('token'),
  loading: false,
  error: null,
  
  login: async (username: string, password: string) => {
    try {
      set({ loading: true, error: null });
      
      const response = await apiLogin(username, password);
      const { token, user_id, username: userName, role } = response;
      
      // Save token to localStorage
      localStorage.setItem('token', token);
      
      set({
        isAuthenticated: true,
        token,
        user: {
          id: user_id,
          username: userName,
          role,
        },
        loading: false,
      });
    } catch (error: any) {
      console.error('Login error:', error);
      set({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: error.response?.data?.error || 'Login failed. Please try again.',
      });
    }
  },
  
  logout: () => {
    localStorage.removeItem('token');
    set({
      isAuthenticated: false,
      user: null,
      token: null,
    });
  },
  
  checkAuth: async () => {
    try {
      const token = localStorage.getItem('token');
      
      if (!token) {
        set({ isAuthenticated: false, user: null });
        return false;
      }
      
      set({ loading: true });
      
      // Get user profile
      const userData = await getUserProfile();
      
      set({
        isAuthenticated: true,
        user: {
          id: userData.id,
          username: userData.username,
          role: userData.profile.role,
        },
        loading: false,
      });
      
      return true;
    } catch (error) {
      console.error('Authentication check failed:', error);
      localStorage.removeItem('token');
      set({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
      });
      return false;
    }
  },
}));

export default useAuthStore; 